{"compilerOptions": {"target": "es2016", "module": "commonjs", "esModuleInterop": true, "experimentalDecorators": true, "forceConsistentCasingInFileNames": true, "strict": true, "skipLibCheck": true, "jsx": "react-native", "allowJs": true, "checkJs": true, "outDir": "dist", "baseUrl": ".", "lib": ["es6"], "noEmit": true, "isolatedModules": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "paths": {"IOTRN/*": ["./*"], "@tracer/*": ["./src/utils/tracer/*"], "@components/*": ["./src/components/*"], "@api/*": ["./src/api/*"], "@config/*": ["./src/config/*"], "@i18n/*": ["./src/i18n/*"], "@assets/*": ["./src/assets/*"], "@utils/*": ["./src/utils/*"], "@pages/*": ["./src/pages/*"], "@base/*": ["./src/base/*"], "@cvn-icon/*": ["./src/utils/cvn-icon/*"], "@types/*": ["./src/types/*"], "@components": ["./src/components"], "@api": ["./src/api"], "@config": ["./src/config"], "@i18n": ["./src/i18n"], "@assets": ["./src/assets"], "@utils": ["./src/utils"], "@pages": ["./src/pages"], "@base": ["./src/base"], "@cvn-icon": ["./src/utils/cvn-icon"], "@tracer": ["./src/utils/tracer"], "@types": ["./src/types"]}, "resolveJsonModule": true, "noImplicitAny": true, "noImplicitReturns": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true}, "include": ["./src/**/*.ts", "./src/**/*.tsx", "./src/**/*.d.ts", "./src/**/*.json", "declaration.d.ts"], "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"]}