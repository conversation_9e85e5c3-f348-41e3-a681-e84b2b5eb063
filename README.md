<!--
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2023-02-09 11:35:27
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-07-24 11:20:15
 * @FilePath: /61004/README.md
 * @Description: 使用文档
-->
#Robot

RN project Robot（React Native 0.63.2）

English | [简体中文](./README-zh_CN.md)

for users outside Chinese mainland, please remove `.npmrc` file.

## Running

```bash
$ npm install && npm run start
# or
$ yarn && yarn start
```

### 监听事件集合
#### CommonEmitter
import {
  CommonEmitter,
} from 'cvn-panel-kit';
CommonEmitter 对应的监听事件，原生和RN传输数据使用，使用示例：
```
 CommonEmitter.addListener('didRecievePushNotification', () => {});
 CommonEmitter.removeAllListeners('didRecievePushNotification');
```
- NAVIGATOR_ON_WILL_FOCUS // 路由即将渲染，原生路由变化前调用，理解为从哪个vc出去
- didRecievePushNotification // RN收到推送消息
- otaUpdateDidCompleted // 升级完成通知
- RNContainerViewWillAppear // 原生VC即将出现
- RNContainerViewWillDisAppear // 原生VC即将消失
- keyBackDown // 安卓 键盘物理返回键
- bluetoothChange // 系统蓝牙开关监听 0：未开启, 1：蓝牙已开启, 2： 表示未授权
- deviceBleConnectStateChange // 0: 断开连接，1: 连接中/开始连接，2: 连接成功,3：连接失败
- localDeviceDataChange // 蓝牙数据传输，JSON字符串
- topicDataDidChange // topic数据监听，区分不同的topic，返回JSON 字符串
- didReadRSSI // 蓝牙信号返回

#### DeviceEventEmitter
RN系统内事件监听，用于RN内不同页面数据传输
- refreshTotalLength  // RN内跨页面，trackHistroy页面 点击确定进行Ruler组件刻度尺回显
使用示例：
```
import {
  DeviceEventEmitter,
} from 'react-native';
// 增加监听
this.initiallistener = DeviceEventEmitter.addListener(
    'refreshTotalLength',
    this.refreshTotalLength,
);
// 移除
this?.initiallistener?.remove();
```
## License
