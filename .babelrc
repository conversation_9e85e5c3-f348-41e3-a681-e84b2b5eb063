{
  "presets": ["module:metro-react-native-babel-preset"],
  "plugins": [
    [
      "import",
      {
        "libraryName": "@ant-design/react-native"
      }
    ],
    [
      "@babel/plugin-proposal-decorators",
      {
        "legacy": true
      }
    ],
    [
      "module-resolver",
      {
        "root": ["./"],
        "alias": {
          "@tracer": "./src/utils/tracer",
          "@api": "./src/api",
          "@components": "./src/components",
          "@config": "./src/config",
          "@i18n": "./src/i18n",
          "@assets": "./src/assets",
          "@utils": "./src/utils",
          "@pages": "./src/pages",
          "@base": "./src/base",
          "@types": "./src/types",
          "IOTRN": "./",
          "@cvn-icon": "./src/utils/cvn-icon"
        }
      }
    ]
    // ["transform-remove-console", { "exclude": [ "error", "warn"] }]
  ],
  "env": {
    "production": {
      "plugins": [
        // "transform-remove-console"
        ["transform-remove-console", {"exclude": ["error", "warn"]}]
      ]
    }
  }
}
