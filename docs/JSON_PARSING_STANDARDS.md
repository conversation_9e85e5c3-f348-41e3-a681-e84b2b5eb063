# JSON解析标准和最佳实践

## 概述

本文档定义了React Native项目中JSON解析的标准和最佳实践，旨在提高代码质量、应用稳定性和用户体验。

## 核心原则

### 1. 安全第一
- **永远不要直接使用 `JSON.parse()`**
- 所有JSON解析操作必须包含错误处理
- 提供合理的默认值和降级策略

### 2. 类型安全
- 使用TypeScript类型注解
- 避免使用 `any` 类型
- 实施运行时类型验证

### 3. 可观测性
- 记录详细的错误日志
- 提供上下文信息便于调试
- 监控解析失败率

## 标准工具

### 统一JSON解析工具

项目提供了统一的JSON解析工具 `src/utils/jsonParser.ts`，包含以下功能：

#### 主要函数

1. **`safeJsonParse<T>(jsonString, options)`** - 完整的安全解析函数
2. **`parseJsonSafely<T>(jsonString, defaultValue, context)`** - 简化版本
3. **`validators`** - 内置类型验证器
4. **`createValidator<T>(predicate, typeName)`** - 自定义验证器工厂

#### 使用示例

```typescript
import {parseJsonSafely, safeJsonParse, validators} from '@utils/jsonParser';

// 基础使用
const data = parseJsonSafely(
  jsonString,
  {}, // 默认值
  'ComponentName.methodName' // 上下文
);

// 高级使用
const result = safeJsonParse<UserData>(jsonString, {
  defaultValue: null,
  validator: validators.object,
  context: 'UserService.parseUserData',
  throwOnError: false,
});

if (result.success) {
  console.log('解析成功:', result.data);
} else {
  console.error('解析失败:', result.error);
}
```

## 使用规范

### 1. 禁止的做法

```typescript
// ❌ 错误：直接使用JSON.parse
const data = JSON.parse(jsonString);

// ❌ 错误：简单的try-catch没有上下文
try {
  const data = JSON.parse(jsonString);
} catch (error) {
  console.log('解析失败');
}

// ❌ 错误：使用any类型
const data: any = JSON.parse(jsonString);
```

### 2. 推荐的做法

```typescript
// ✅ 正确：使用统一工具
const data = parseJsonSafely(
  jsonString,
  {name: '', age: 0}, // 类型安全的默认值
  'UserProfile.loadData'
);

// ✅ 正确：带类型验证
const deviceData = safeJsonParse<DeviceInfo>(jsonString, {
  defaultValue: null,
  validator: (data): data is DeviceInfo => 
    typeof data === 'object' && 
    data !== null && 
    'deviceId' in data,
  context: 'DeviceStore.parseDeviceInfo',
});

// ✅ 正确：处理解析结果
if (deviceData.success && deviceData.data) {
  this.updateDeviceInfo(deviceData.data);
} else {
  logger.warn('设备信息解析失败，使用默认配置');
  this.useDefaultDeviceConfig();
}
```

### 3. 上下文命名规范

上下文字符串应遵循以下格式：
- `ClassName.methodName` - 类方法中的解析
- `ComponentName.eventHandler` - 组件事件处理中的解析
- `ServiceName.apiMethod` - API服务中的解析
- `StoreName.action` - MobX Store动作中的解析

示例：
```typescript
'DeviceStore.parseDeviceDetail'
'BatteryManagement.parseBluetoothData'
'MessageCenter.parseNotificationData'
'BleConnectionManager.handleTopicData'
```

## 类型验证

### 内置验证器

```typescript
import {validators} from '@utils/jsonParser';

// 基础类型验证
validators.string(data)     // 字符串
validators.number(data)     // 数字
validators.boolean(data)    // 布尔值
validators.array(data)      // 数组
validators.object(data)     // 对象

// 复合验证器
validators.nonEmptyString(data)   // 非空字符串
validators.nonEmptyArray(data)    // 非空数组
validators.positiveNumber(data)   // 正数
```

### 自定义验证器

```typescript
import {createValidator} from '@utils/jsonParser';

// 创建设备状态验证器
const isValidDeviceStatus = createValidator<DeviceStatus>(
  (data) => {
    return typeof data === 'object' &&
           data !== null &&
           'status' in data &&
           'timestamp' in data &&
           typeof data.status === 'number' &&
           [0, 1, 2, 3].includes(data.status);
  },
  'DeviceStatus'
);

// 使用自定义验证器
const result = safeJsonParse(jsonString, {
  validator: isValidDeviceStatus,
  defaultValue: {status: 0, timestamp: Date.now()},
  context: 'DeviceMonitor.parseStatus',
});
```

## 错误处理策略

### 1. 分级处理

- **Critical**: 影响核心功能的解析失败，需要用户感知
- **Warning**: 影响次要功能，使用默认值继续
- **Info**: 不影响功能，仅记录日志

### 2. 降级策略

```typescript
// 示例：设备配置解析失败的降级策略
const deviceConfig = parseJsonSafely(
  configString,
  getDefaultDeviceConfig(), // 使用默认配置
  'DeviceConfig.parse'
);

// 如果解析失败，记录错误但不影响应用运行
if (!deviceConfig.success) {
  logger.error('设备配置解析失败，使用默认配置', {
    error: deviceConfig.error,
    fallbackConfig: getDefaultDeviceConfig(),
  });
  
  // 可选：通知用户配置可能不是最新的
  showWarningToast('设备配置加载异常，部分功能可能受限');
}
```

## 性能考虑

### 1. 避免重复解析

```typescript
// ❌ 错误：重复解析同一数据
const data1 = parseJsonSafely(jsonString, {}, 'Context1');
const data2 = parseJsonSafely(jsonString, {}, 'Context2');

// ✅ 正确：解析一次，多处使用
const parsedData = parseJsonSafely(jsonString, {}, 'DataParser');
const processedData1 = processData1(parsedData);
const processedData2 = processData2(parsedData);
```

### 2. 大数据处理

对于大型JSON数据，考虑：
- 分块处理
- 异步解析
- 内存使用监控

## 监控和调试

### 1. 日志记录

所有解析失败都会自动记录到日志系统，包含：
- 错误类型和消息
- 输入数据预览（前100字符）
- 上下文信息
- 堆栈跟踪

### 2. 性能监控

建议监控以下指标：
- JSON解析成功率
- 解析耗时分布
- 常见错误类型
- 影响的功能模块

## 迁移指南

### 现有代码迁移

1. **识别所有JSON.parse调用**
   ```bash
   grep -r "JSON\.parse" src/
   ```

2. **逐个替换**
   - 分析每个调用的上下文
   - 确定合适的默认值
   - 添加类型注解
   - 使用统一工具替换

3. **测试验证**
   - 单元测试覆盖
   - 边界情况测试
   - 错误场景测试

### 代码审查清单

- [ ] 是否使用了统一的JSON解析工具？
- [ ] 是否提供了合理的默认值？
- [ ] 是否包含了上下文信息？
- [ ] 是否有适当的类型注解？
- [ ] 是否处理了解析失败的情况？
- [ ] 是否符合命名规范？

## 总结

通过遵循这些标准和最佳实践，我们可以：

1. **提高应用稳定性** - 避免因JSON解析失败导致的崩溃
2. **改善用户体验** - 提供优雅的错误处理和降级策略
3. **增强可维护性** - 统一的工具和规范便于维护
4. **提升开发效率** - 减少调试时间，提高代码质量
5. **保证类型安全** - TypeScript类型系统的充分利用

记住：**安全的JSON解析是高质量React Native应用的基础**。
