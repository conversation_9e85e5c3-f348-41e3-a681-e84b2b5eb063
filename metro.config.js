/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2024-05-24 15:30:49
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-07-10 14:53:39
 * @FilePath: /Robot/metro.config.js
 * @Description: 打包配置
 *
 */
const {getDefaultConfig, mergeConfig} = require('@react-native/metro-config');
const Mcs = require('@cvn/metro-code-split');
const {removeConflictMetroFields} = require('./metro.helper');

const mcs = new Mcs({
  dll: {
    entry: ['react-native', 'react'], // which three - party library into dll
    referenceDir: './public/dll', // the JSON address to reference for the build DLL file, also the npm run build:dllJson output directory
  },
  dynamicImports: false, // DynamicImports can also be set to false to disable this feature if it is not required
});

/**
 * Metro configuration
 * https://facebook.github.io/metro/docs/configuration
 *
 * @type {import('metro-config').MetroConfig}
 */
const config = {};

const defaultConfig = mergeConfig(getDefaultConfig(__dirname), config);

module.exports =
  process.env.NODE_ENV === 'production'
    ? mcs.mergeTo(removeConflictMetroFields(defaultConfig))
    : defaultConfig;
// const Mcs = require('@cvn/metro-code-split')

// const mcs = new Mcs({
// dll: {
// entry: ['react-native', 'react'], // which three - party library into dll
// referenceDir: './public/dll', // the JSON address to reference for the build DLL file, also the npm run build:dllJson output directory
// },
// dynamicImports: {}, // DynamicImports can also be set to false to disable this feature if it is not required
// })

// const busineConfig = {
// transformer: {
// getTransformOptions: async () => ({
// transform: {
// experimentalImportSupport: false,
// inlineRequires: true,
// },
// }),
// },
// }

// module.exports = process.env.NODE_ENV === 'production' ? mcs.mergeTo(busineConfig) : busineConfig
