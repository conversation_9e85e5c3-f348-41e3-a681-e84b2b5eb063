/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2023-02-09 11:35:29
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-08-27 11:13:57
 * @FilePath: /61004/src/api/ApiMap
 * @Description: api统一管理
 *
 */
import {CVNSdk} from 'cvn-panel-kit';

const apiMap = {
  deviceDetail: '/device/detail', // 设备详情
  deviceUnbind: '/device/unbind', // 设备解绑
  resetPassword: '/device/shadow/thing/model/update', // 重置密码
  deviceEdit: '/device/edit', // 设备编辑
  getPartsOverview: '/device/parts/overview', // 配件入口数据
  usageHistory: '/data/usageHistory', // 使用历史,折线图
  getDeviceFaultMessage: '/device/getDeviceFaultMessage', // 设备故障消息,首页
  getDeviceFaultList: '/device/getDeviceFaultList', // 设备故障列表
  fetchWhetherDeviceHasUnreadMessage: '/message/device/unread/check', // 获取设备是否有未读消息
};

interface UsageHistoryProp {
  dateType: number; // 请求的日期类型：1:日期，2:周，3:月份
  dateValue: string; // 周场景入参： "2024/01/05-2024/01/10" 月场景入参： "2024/01"
  busType: string[]; // 1 查询的业务数据类型：1：工作时长 2：二氧化碳减排量 3：割草面积 }
  deviceId: string;
  datePeriod: number;
}

interface ParamProp {
  deviceId: string;
}

/**
 * @description: 设备详情 暴露接口
 * @param {Object} params
 * @param {Object} options
 */

/**
 * @description: 获取消息列表未读消息
 * @param {Object} params
 * @param {Object} options
 * @return {Promise} 用于外部promise调用
 */
export const postToFetchWhetherDeviceHasUnreadMessage = (
  params: ParamProp,
  options?: {showLoading?: boolean},
) => {
  return CVNSdk.apiRequest(apiMap.fetchWhetherDeviceHasUnreadMessage, params, {
    showLoading: options?.showLoading,
  });
};

/**
 * @description: 实际掉设备详情接口
 * @param {Object} params
 * @param {Object} options
 * @return {Promise} 用于外部promise调用
 */
export const getDeviceDetail = (
  params: {req: string},
  options?: {showLoading: boolean},
) => {
  return CVNSdk.apiRequest(apiMap.deviceDetail, params, {
    showLoading: options?.showLoading,
  });
};

/**
 * @description: 实际调用以获取零部件详情
 * @param {Object} params - 获取零部件详情所需的参数
 * @param {Object} options - 额外选项，可选
 * @return {Promise} 用于外部承诺调用
 */
export const getPartsOverview = (
  params: {req: string},
  options = {showLoading: false},
) => {
  return CVNSdk.apiRequest(apiMap.getPartsOverview, params, {
    showLoading: options?.showLoading,
  });
};

/**
 * @description: 获取上一次使用历史数据
 * @param {Object} params
 * @param {Object} options
 * @return {Promise}
 */
export const usageHistory = (
  params: UsageHistoryProp,
  options = {showLoading: false},
) => {
  const {showLoading = true} = options;
  return CVNSdk.apiRequest(apiMap.usageHistory, params, {
    showLoading,
  });
};

/**
 * @description: 设备解绑
 * @param {Object} params
 * @return {Promise} 用于外部promise调用
 */
export const deviceUnbind = (params: ParamProp) => {
  return CVNSdk.apiRequest(apiMap.deviceUnbind, params);
};

/**
 * @description: 设备解绑重置密码
 * @param {Object} params
 * @return {Promise} 用于外部promise调用
 */
export const deviceResetPassword = (params: {
  deviceId: string;
  identifier: string;
  value: boolean;
}) => {
  return CVNSdk.apiRequest(apiMap.resetPassword, params);
};

/**
 * @description: 设备编辑
 * @param {Object} params
 * @return {Promise} 用于外部promise调用
 */
export const deviceEdit = (params: {
  deviceId: string;
  deviceNickname: string;
}) => {
  return CVNSdk.apiRequest(apiMap.deviceEdit, params);
};

/**
 * @description: 设备首页故障消息接口
 * @param {Object} params
 * @param {Object} options
 * @return {Promise} 用于promise调用
 */
export const fetchHomeMessageList = (
  params: ParamProp,
  options = {showLoading: false},
) => {
  const {showLoading} = options;
  return CVNSdk.apiRequest(apiMap.getDeviceFaultMessage, params, {
    showLoading: showLoading,
  });
};
/**
 * @description: 设备故障列表接口
 * @param {Object} params
 * @param {Object} options
 * @return {Promise} 用于promise调用
 */
export const fetchDeviceErrorList = (
  params: ParamProp,
  options = {showLoading: false},
) => {
  const {showLoading} = options;
  return CVNSdk.apiRequest(apiMap.getDeviceFaultList, params, {
    showLoading: showLoading,
  });
};

export default apiMap;
