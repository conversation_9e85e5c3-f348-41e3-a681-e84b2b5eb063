import React from 'react';
import {StyleSheet, View, Text} from 'react-native';

/**
 * @description 百分百常量
 */
export const PERCENT = 100;
export const PercentView = ({style = {}, value = 0}) => {
  const formatValue = value >= PERCENT ? PERCENT : value;
  return (
    <View style={[styles.percentView, style]}>
      <Text style={[styles.percentText]}>{formatValue}</Text>
      <Text style={[styles.symbolText]}>%</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  percentView: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  percentText: {
    fontSize: 20,
    color: '#FFFFFF',
    fontWeight: 'bold',
    lineHeight: 24,
  },
  symbolText: {
    fontSize: 11,
    color: '#FFFFFF',
    fontWeight: 'bold',
    lineHeight: 24,
  },
});
export default PercentView;
