import React from 'react';
import {StyleSheet, ViewStyle} from 'react-native';
import {CVNIcon} from 'cvn-panel-kit';
import {PercentView} from './PercentView';
import PropTypes from 'prop-types';
import {BatteryDetailInfo} from '../../../types';

interface BatteryIconViewProps {
  item: BatteryDetailInfo;
  style?: ViewStyle;
}

export const BatteryIconView = ({item, style}: BatteryIconViewProps) => {
  const {didHave = false, value = 0, isError, overTemp} = item;

  let backSource = require('@assets/battery/61004_icon_battery_normal.png');

  if (!didHave) {
    backSource = require('@assets/battery/61004_icon_battery_empty.png');
  } else if (isError) {
    backSource = require('@assets/battery/61004_icon_battery_error.png');
  } else if (overTemp) {
    backSource = require('@assets/battery/61004_icon_battery_over_temprature.png');
  }

  return (
    <CVNIcon style={[style, styles.batteryStyle]} source={backSource}>
      {didHave && <PercentView value={Number(value)} />}
    </CVNIcon>
  );
};
BatteryIconView.propTypes = {
  item: PropTypes.object,
  style: PropTypes.object,
};
const styles = StyleSheet.create({
  batteryStyle: {
    backgroundColor: 'transparent',
    justifyContent: 'center',
    alignContent: 'center',
    borderRadius: 4,
  },
});

export default BatteryIconView;
