/**
 * @description 错误边界使用示例
 */

import React, {useState, useCallback} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import {
  ErrorBoundary,
  withPageErrorBoundary,
  withComponentErrorBoundary,
  useErrorHandler,
  useAsyncErrorHandler,
  useErrorRetry,
  ErrorFallbackProps,
} from '../../components/ErrorBoundary';

// 故意抛出错误的组件
const BuggyComponent: React.FC<{shouldThrow: boolean}> = ({shouldThrow}) => {
  if (shouldThrow) {
    throw new Error('这是一个故意抛出的错误用于测试错误边界');
  }
  return <Text style={styles.successText}>组件正常渲染</Text>;
};

// 使用组件级错误边界包装的组件
const SafeBuggyComponent = withComponentErrorBoundary(
  BuggyComponent,
  'BuggyComponent',
);

// 自定义错误回退组件
const CustomErrorFallback: React.FC<ErrorFallbackProps> = ({
  error,
  resetError,
  canRetry,
}) => {
  const handleReset = useCallback((): void => {
    resetError();
  }, [resetError]);

  return (
    <View style={styles.customErrorContainer}>
      <Text style={styles.customErrorTitle}>自定义错误界面</Text>
      <Text style={styles.customErrorMessage}>
        {error?.message ?? '未知错误'}
      </Text>
      {canRetry && (
        <TouchableOpacity
          style={styles.customErrorButton}
          onPress={handleReset}>
          <Text style={styles.customErrorButtonText}>重试</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

// 异步错误组件
const AsyncErrorComponent: React.FC = () => {
  const {executeAsync} = useAsyncErrorHandler();
  const [result, setResult] = useState<string>('');

  const handleAsyncError = useCallback(async (): Promise<void> => {
    const asyncResult = await executeAsync(async () => {
      // 模拟异步操作失败
      await new Promise<void>(resolve => {
        setTimeout(resolve, 1000);
      });
      throw new Error('异步操作失败');
    }, 'AsyncErrorComponent');

    if (asyncResult) {
      setResult('异步操作成功');
    } else {
      setResult('异步操作失败，已被错误处理器捕获');
    }
  }, [executeAsync]);

  return (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>异步错误处理示例</Text>
      <TouchableOpacity style={styles.button} onPress={handleAsyncError}>
        <Text style={styles.buttonText}>触发异步错误</Text>
      </TouchableOpacity>
      {result ? <Text style={styles.resultText}>{result}</Text> : null}
    </View>
  );
};

// 重试机制示例组件
const RetryComponent: React.FC = () => {
  const {retry, retryCount, isRetrying, canRetry, reset} = useErrorRetry(3);
  const [status, setStatus] = useState<string>('');

  const simulateFailingOperation = useCallback(async (): Promise<string> => {
    // 模拟一个有70%概率失败的操作
    if (Math.random() < 0.7) {
      throw new Error(`操作失败 (尝试 ${retryCount + 1})`);
    }
    return '操作成功！';
  }, [retryCount]);

  const handleRetryOperation = useCallback(async (): Promise<void> => {
    const success = await retry(async () => {
      setStatus('正在尝试操作...');
      const result = await simulateFailingOperation();
      setStatus(result);
    }, 1000);

    if (!success && !canRetry) {
      setStatus('操作失败，已达到最大重试次数');
    }
  }, [retry, simulateFailingOperation, canRetry]);

  const handleReset = useCallback((): void => {
    reset();
    setStatus('');
  }, [reset]);

  return (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>重试机制示例</Text>
      <Text style={styles.infoText}>
        重试次数: {retryCount}/3 | 可重试: {canRetry ? '是' : '否'}
      </Text>

      <View style={styles.buttonRow}>
        <TouchableOpacity
          style={[styles.button, isRetrying && styles.disabledButton]}
          onPress={handleRetryOperation}
          disabled={isRetrying || !canRetry}>
          <Text style={styles.buttonText}>
            {isRetrying ? '重试中...' : '开始操作'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.secondaryButton} onPress={handleReset}>
          <Text style={styles.secondaryButtonText}>重置</Text>
        </TouchableOpacity>
      </View>

      {status ? <Text style={styles.resultText}>{status}</Text> : null}
    </View>
  );
};

// 手动错误处理示例
const ManualErrorComponent: React.FC = () => {
  const {error, captureError, clearError, hasError} = useErrorHandler();

  const triggerManualError = useCallback((): void => {
    const manualError = new Error('这是一个手动捕获的错误');
    captureError(manualError, 'ManualErrorComponent');
  }, [captureError]);

  const handleClearError = useCallback((): void => {
    clearError();
  }, [clearError]);

  return (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>手动错误处理示例</Text>

      {hasError ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>捕获到错误: {error?.message}</Text>
          <TouchableOpacity
            style={styles.clearButton}
            onPress={handleClearError}>
            <Text style={styles.buttonText}>清除错误</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <TouchableOpacity style={styles.button} onPress={triggerManualError}>
          <Text style={styles.buttonText}>触发手动错误</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

// 主组件
const ErrorBoundaryExample: React.FC = () => {
  const [shouldThrowError, setShouldThrowError] = useState<boolean>(false);

  const showAlert = useCallback((title: string, message: string): void => {
    Alert.alert(title, message, [{text: '确定'}]);
  }, []);

  const toggleError = useCallback((): void => {
    setShouldThrowError(prev => !prev);
  }, []);

  const handleErrorBoundaryError = useCallback(
    (error: Error): void => {
      showAlert('错误边界触发', `捕获到错误: ${error.message}`);
    },
    [showAlert],
  );

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>错误边界使用示例</Text>

      {/* 基本错误边界示例 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>基本错误边界示例</Text>
        <TouchableOpacity style={styles.button} onPress={toggleError}>
          <Text style={styles.buttonText}>
            {shouldThrowError ? '修复组件' : '破坏组件'}
          </Text>
        </TouchableOpacity>

        <ErrorBoundary
          level="component"
          name="BasicExample"
          enableRetry={true}
          maxRetries={2}
          onError={handleErrorBoundaryError}>
          <BuggyComponent shouldThrow={shouldThrowError} />
        </ErrorBoundary>
      </View>

      {/* 自定义错误UI示例 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>自定义错误UI示例</Text>
        <ErrorBoundary
          level="component"
          name="CustomUIExample"
          fallback={CustomErrorFallback}
          enableRetry={true}
          maxRetries={1}>
          <BuggyComponent shouldThrow={shouldThrowError} />
        </ErrorBoundary>
      </View>

      {/* 高阶组件包装示例 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>高阶组件包装示例</Text>
        <Text style={styles.infoText}>
          这个组件使用 withComponentErrorBoundary 包装
        </Text>
        <SafeBuggyComponent shouldThrow={shouldThrowError} />
      </View>

      {/* 异步错误处理示例 */}
      <AsyncErrorComponent />

      {/* 重试机制示例 */}
      <RetryComponent />

      {/* 手动错误处理示例 */}
      <ManualErrorComponent />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#2c3e50',
  },
  section: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
    color: '#34495e',
  },
  infoText: {
    fontSize: 14,
    color: '#7f8c8d',
    marginBottom: 12,
  },
  button: {
    backgroundColor: '#3498db',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 6,
    marginVertical: 8,
  },
  secondaryButton: {
    backgroundColor: '#95a5a6',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 6,
    marginVertical: 8,
  },
  clearButton: {
    backgroundColor: '#e74c3c',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 6,
    marginTop: 8,
  },
  disabledButton: {
    backgroundColor: '#bdc3c7',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  buttonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  secondaryButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
  successText: {
    color: '#27ae60',
    fontSize: 16,
    textAlign: 'center',
    marginTop: 12,
  },
  resultText: {
    fontSize: 14,
    color: '#2c3e50',
    marginTop: 12,
    textAlign: 'center',
  },
  errorContainer: {
    backgroundColor: '#ffeaa7',
    borderColor: '#fdcb6e',
    borderWidth: 1,
    borderRadius: 6,
    padding: 12,
  },
  errorText: {
    color: '#e17055',
    fontSize: 14,
    textAlign: 'center',
  },
  customErrorContainer: {
    backgroundColor: '#ffe6e6',
    borderColor: '#ff9999',
    borderWidth: 2,
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
  },
  customErrorTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#cc0000',
    marginBottom: 8,
  },
  customErrorMessage: {
    fontSize: 14,
    color: '#cc0000',
    textAlign: 'center',
    marginBottom: 12,
  },
  customErrorButton: {
    backgroundColor: '#cc0000',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 4,
  },
  customErrorButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
  },
});

// 使用页面级错误边界包装
export default withPageErrorBoundary(
  ErrorBoundaryExample,
  'ErrorBoundaryExample',
);
