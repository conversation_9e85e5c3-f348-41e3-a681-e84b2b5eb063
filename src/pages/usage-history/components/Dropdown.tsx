import {CVNIcon} from 'cvn-panel-kit';
import React, {memo, useEffect, useState} from 'react';
import {
  Modal,
  View,
  StyleSheet,
  Text,
  TouchableWithoutFeedback,
  ViewStyle,
} from 'react-native';
/**
 * 菜单弹窗组件
 * @param {number} defaultIndex - 默认选中索引
 * @param {boolean} visible - 弹窗可见状态
 * @param {Function} setModalVisible - 设置弹窗可见状态函数
 * @param {Function} onItemSelected - 选中项回调函数
 * @param {Array} data - 数据数组
 * @param {Object} modalViewstyle - 弹窗样式
 * @returns {Node} - Dropdown组件
 */
interface DropdownProps {
  defaultIndex?: number;
  visible?: boolean;
  setModalVisible?: (visible: boolean) => void;
  onItemSelected: (index: number) => void;
  data?: {title: string}[];
  modalViewstyle?: ViewStyle;
  onClose?: () => void;
}

const Dropdown = ({
  defaultIndex = 0,
  visible = false,
  setModalVisible = () => {},
  onItemSelected,
  data = [],
  modalViewstyle = {},
  onClose = () => {},
}: DropdownProps) => {
  const [selectedRow, setSelectedRow] = useState(defaultIndex);

  useEffect(() => {
    setSelectedRow(defaultIndex);
  }, [defaultIndex]);

  const selectRow = (index: number) => {
    setSelectedRow(index);
    setModalVisible(false);
    onItemSelected(index);
  };

  return (
    <View style={styles.container}>
      <Modal transparent={true} visible={visible}>
        <TouchableWithoutFeedback onPress={onClose}>
          <View style={styles.modalBackground}>
            <View style={[styles.modalView, modalViewstyle]}>
              {data.map((item, index) => (
                <TouchableWithoutFeedback
                  key={item.title}
                  onPress={() => selectRow(index)}>
                  <View
                    style={[
                      styles.row,
                      selectedRow === index && styles.selRow,
                    ]}>
                    <Text>{item.title}</Text>
                    {selectedRow === index && (
                      <CVNIcon
                        source={require('@assets/common/61004_common_icon_checked.png')}
                      />
                    )}
                  </View>
                </TouchableWithoutFeedback>
              ))}
            </View>
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    </View>
  );
};

export default memo(Dropdown);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalBackground: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalView: {
    marginLeft: 17,
    marginTop: 138,
    width: 195,
    height: 250,
    backgroundColor: '#fff',
    borderRadius: 8,
    overflow: 'hidden',
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingRight: 20,
    paddingLeft: 15,
    height: 50,
  },
  selRow: {
    backgroundColor: '#77BC1F1A',
  },
  itemBox: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 40,
  },
});
