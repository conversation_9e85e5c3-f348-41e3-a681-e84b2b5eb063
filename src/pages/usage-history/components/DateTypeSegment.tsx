/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2023-09-27 10:14:59
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-07-17 15:32:13
 * @FilePath: /61004/src/pages/usage-history/components/DateTypeSegment
 * @Description: 时间类型切换卡片
 */
import React, {memo} from 'react';
import SegmentView from './SegmentView';
import Strings from '@i18n';
/**
 * 日期类型分段选择器组件
 * @param {string} title - 标题
 * @param {number} value - 当前选中的值
 * @param {Function} onItemSelect - 选中项回调函数
 * @returns {Node} - DateTypeSegment组件
 */
interface Props {
  title?: string;
  onItemSelect: (index: number) => void;
}
const DateTypeSegment = ({
  title = Strings.getLang('rn_61004_panelhome_speedtitle_textview_text'),
  onItemSelect,
}: Props) => {
  const data = [
    {
      title: Strings.getLang('rn_61004_usagehistory_day_textview_text'),
    },
    {
      title: Strings.getLang('rn_61004_usagehistory_week_textview_text'),
    },
    {
      title: Strings.getLang('rn_61004_usagehistory_month_textview_text'),
    },
  ];
  return (
    <SegmentView
      key={title}
      data={data}
      onItemSelect={(_, index: number) => {
        onItemSelect(index);
      }}
    />
  );
};
export default memo(DateTypeSegment);
