/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2023-10-12 14:04:33
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-07-17 15:37:18
 * @FilePath: /61004/src/pages/usage-history/components/SegmentView
 * @Description: segment卡片
 */
import React, {useState, memo} from 'react';
import {
  View,
  StyleSheet,
  Text,
  TouchableWithoutFeedback,
  ViewStyle,
} from 'react-native';
/**
 * 分段选择视图组件
 * @param {Object} style - 自定义样式
 * @param {Function} onItemSelect - 选中项回调函数
 * @param {Array} data - 数据数组
 * @param {number} value - 当前选中值
 * @returns {Node} - SegmentView组件
 */
interface Props {
  style?: ViewStyle;
  onItemSelect?: (item: {title: string}, index: number) => void;
  data: {title: string}[];
}
const SegmentView = ({style = {}, onItemSelect = () => {}, data}: Props) => {
  const [selIndex, setSelIndex] = useState(0);
  return (
    <View style={[styles.container, style]}>
      {data?.map((item, index) => {
        const selected = selIndex === index;
        return (
          <TouchableWithoutFeedback
            key={item.title}
            onPress={() => {
              setSelIndex(index);
              onItemSelect(item, index);
            }}>
            <View
              style={[
                styles.buttonView,
                selected ? styles.activeView : styles.inActiveView,
              ]}>
              <Text
                style={[
                  styles.titleText,
                  selected ? styles.activeText : styles.inActiveText,
                ]}>
                {item.title}
              </Text>
            </View>
          </TouchableWithoutFeedback>
        );
      })}
    </View>
  );
};

export default memo(SegmentView);

const styles = StyleSheet.create({
  container: {
    padding: 2,
    marginTop: 14,
    marginHorizontal: 16,
    backgroundColor: '#EDEEEF',
    borderRadius: 7,
    flexDirection: 'row',
    alignItems: 'center',
  },
  buttonView: {
    borderRadius: 6,
    height: 42,
    width: '33.33%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  activeView: {
    backgroundColor: '#fff',
  },
  inActiveView: {
    backgroundColor: 'transparent',
  },
  titleText: {
    fontSize: 13,
    fontWeight: '400',
    color: '#000000',
  },
  activeText: {
    fontSize: 14,
    color: '#000000',
    fontWeight: '400',
  },
  inActiveText: {
    color: '#000000',
    fontWeight: '500',
  },
});
