/*
 * @Description: 页面返回
 */
import {device} from 'cvn-panel-kit';
import {tracer, eleIdMap} from '@tracer';
import {AppState, NativeEventSubscription} from 'react-native';
import {RootStore} from 'IOTRN/src/mobx/rootStore';
import DeviceStore from 'IOTRN/src/mobx/deviceStore';
import DeviceConnectionStore from 'IOTRN/src/mobx/deviceConnectionStore';

export default class PageBack {
  deviceStore: DeviceStore;
  deviceConnectionStore: DeviceConnectionStore;
  appChangeListener: NativeEventSubscription | null = null;
  constructor(store: RootStore) {
    this.deviceStore = store.deviceStore;
    this.deviceConnectionStore = store.deviceConnectionStore;
  }

  onHeaderTitlePress = (callBack: (result?: string) => void) => {
    const {deviceDetail} = this.deviceStore.state;
    const defaultName = deviceDetail?.nickName
      ? deviceDetail?.nickName
      : deviceDetail?.deviceName;
    tracer.click({
      eleid: eleIdMap.Equipment_Name_Button_Click,
    });
    if (callBack) {
      callBack(defaultName);
    }
  };

  /**
   * @description: app后台切到前台监听
   */
  dealWithAppDidComeForeground = () => {
    // 监听状态改变事件
    this.appChangeListener = AppState.addEventListener(
      'change',
      this.handleAppStateChange,
    );
  };
  /**
   * @description: app前后调切换处理,割草轨迹，防止app进入后台一段时间后切回来，数据展示不是最新
   * @param {String} appState
   */
  handleAppStateChange = (appState: string) => {
    if (appState === 'active') {
      this.deviceConnectionStore.actions.setAppDidBecomeActive(true);

      const {mac} = this.deviceStore.state.initialParams;
      const {bleConnected} = this.deviceConnectionStore.state;
      if (!bleConnected) {
        // 如果蓝牙未连接，则重新连接蓝牙
        device.requestBleConnect(mac);
      }
      // 为了防止aws通道无数据，再次订阅topic
      this.deviceStore.actions.subscribeWithWIFI();
      if (this.deviceConnectionStore.state.connected) {
        this.deviceStore.actions.getOnceDataWithWIFI();
      }
    }
  };

  /**
   * @description: 销毁监听
   */
  destroy = () => {
    this.appChangeListener?.remove();
  };
}
