/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2023-10-12 14:04:33
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-07-02 16:32:28
 * @FilePath: /61004/src/pages/panel/utils/format
 * @Description: 工具类
 */
export const convertToAMPM = (time24: number) => {
  if (time24 < 0 || time24 > 2399) {
    return '无效的时间';
  }
  let hours = Math.floor(time24 / 100);
  const minutes = time24 % 100;
  let ampm = 'AM';

  if (hours >= 12) {
    ampm = 'PM';
    if (hours >= 12) {
      hours -= 12;
    }
  }
  const formattedTime = `${hours}:${minutes
    .toString()
    .padStart(2, '0')}${ampm}`;
  return formattedTime;
};
