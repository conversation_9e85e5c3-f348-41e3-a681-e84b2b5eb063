import React, {memo, useEffect} from 'react';
import {View, StyleSheet, Text, ViewStyle} from 'react-native';
import {CVNIcon} from 'cvn-panel-kit';
import Strings from '@i18n';
import {observer} from 'mobx-react';
import DeviceConnectionStore from 'IOTRN/src/mobx/deviceConnectionStore';

interface InjectedStores {
  style: ViewStyle;
  deviceConnectionStore: DeviceConnectionStore;
  disabled: boolean;
}
const RuncarCard = observer(
  ({style = {}, disabled, deviceConnectionStore}: InjectedStores) => {
    useEffect(() => {
      deviceConnectionStore.actions.getRemainingTimeInfo();
    }, [deviceConnectionStore.state.resWithNew, deviceConnectionStore.actions]);

    const {remainMowingTimeTimeInfo} = deviceConnectionStore.state;
    const {hour, minute} = remainMowingTimeTimeInfo || {};
    const runtimeSource = require('@assets/run/61004_runtime_remaining.png');
    const runningSource = require('@assets/run/61004_run_speed.png');
    return (
      <View style={[styles.container, style]}>
        <View style={styles.cardBox}>
          <View style={styles.valueCard}>
            <CVNIcon
              style={styles.iconStyle}
              source={runtimeSource}
              size={20}
            />
            {!disabled ? (
              <>
                <Text style={styles.titleText}>{hour || '--'}</Text>
                <Text style={[styles.textView, styles.topOffset]}>hr</Text>
                <Text style={styles.titleText}>{minute || '--'}</Text>
                <Text style={[styles.textView, styles.topOffset]}>min</Text>
              </>
            ) : (
              <Text style={styles.titleText}>--</Text>
            )}
          </View>
          <Text style={styles.content}>
            {Strings.getLang(
              'rn_61004_panelhome_runtimeremaining_textview_text',
            )}
          </Text>
        </View>
        <View style={styles.line} />
        <View style={styles.cardBox}>
          <View style={styles.valueCard}>
            <CVNIcon
              style={styles.iconStyle}
              source={runningSource}
              size={20}
            />
            {!disabled ? (
              <>
                <Text style={styles.titleText}>
                  {deviceConnectionStore.actions.bladeSpeed}
                </Text>
                <Text style={[styles.textView, styles.topOffset]}>Mph</Text>
              </>
            ) : (
              <Text style={styles.titleText}>--</Text>
            )}
          </View>
          <Text style={styles.content}>
            {Strings.getLang('rn_61004_panelhome_runningspeed_textview_text')}
          </Text>
        </View>
      </View>
    );
  },
);
export default memo(RuncarCard);

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
  },
  cardBox: {
    display: 'flex',
    width: '50%',
    alignItems: 'center',
  },
  line: {
    width: 1,
    height: 44,
    backgroundColor: 'rgba(0, 0, 0, 0.15)',
  },
  valueCard: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  iconStyle: {
    top: -10,
  },
  titleText: {
    fontSize: 30,
    fontWeight: '500',
    marginLeft: 5,
    color: '#000',
  },
  content: {
    fontSize: 13,
    color: '#666',
  },
  topOffset: {
    top: -3,
  },
  textView: {
    fontSize: 15,
    fontWeight: '500',
    marginLeft: 2,
    color: '#000',
  },
  nameText: {
    fontSize: 14,
    color: '#666666',
  },
  valueText: {
    marginTop: 3.5,
    fontSize: 16,
    color: '#000000',
  },
});
