import {StyleSheet, Text, View} from 'react-native';
import React, {memo, useMemo} from 'react';
import {observer} from 'mobx-react';
import {mobile, Utils} from 'cvn-panel-kit';
import RectView from '@pages/panel/components/RectView';
import Strings from '@i18n';
import {tracer, eleIdMap} from '@tracer';
import {RootStore} from 'IOTRN/src/mobx/rootStore';
import {jumpToParts} from 'IOTRN/src/mobx/utils/help';

const {JumpUtils} = Utils;
interface InjectStore {
  rootStore: RootStore;
  disabled: boolean;
}

const registeredStatus = '1';
const CardList = observer(({rootStore, disabled}: InjectStore) => {
  /**
   * "type":1,//显示类型 1 Available 2 Required
   * "number":10,//页面显示的数量
   */
  const {number: needFixNum, type = 1} =
    rootStore.deviceConnectionStore.state.partsDetail;
  const {deviceDetail, initialParams} = rootStore.deviceStore.state;
  const {showRed, customVersion} = rootStore.deviceConfigurationStore.state;
  const registered = useMemo(
    () => String(deviceDetail?.infoStatus) === registeredStatus,
    [deviceDetail?.infoStatus],
  );

  let cTextStyle = {};
  let subTitle = `${needFixNum || 0} ${Strings.getLang(
    'rn_61004_panelhome_partsavailable_textview_text',
  )}`;
  if (type - 0 === 2) {
    cTextStyle = {color: '#DA322B'};
    subTitle = `${needFixNum || 0} ${Strings.getLang(
      'rn_61004_panelhome_partsmaintenance_textview_text',
    )}`;
  }

  let updateTitle = Strings.getLang(
    'rn_61004_panelhome_upgradedate_textview_text',
  );
  if (showRed) {
    updateTitle = Strings.getLang(
      'rn_61004_panelhome_upgradeavailable_textview_text',
    );
  }
  if (disabled) {
    updateTitle = Strings.getLang('rn_common_devicemsg_version_textview_text');
  }

  const updateValueText = disabled
    ? '- -'
    : Strings.getLang('rn_61004_panelhome_otaver_textview_text') +
      (customVersion || '- -');

  // Define constants for upgradeModel values
  const UPGRADE_MODEL = {
    WIFI: 0,
    BLUETOOTH: 1,
  };

  return (
    <>
      {/* 升级、注册、其他 */}
      <View style={styles.rectsNoTop}>
        <RectView
          disabled={!rootStore.deviceStore.actions.canBeShared}
          icon={
            registered
              ? require('@assets/home/<USER>')
              : require('@assets/home/<USER>')
          }
          title={
            registered
              ? Strings.getLang('rn_61004_panelhome_registration_textview_text')
              : Strings.getLang('rn_61004_panelhome_registernow_textview_text')
          }
          onPress={() => {
            tracer.click({
              eleid: eleIdMap.Device_Registration_Button_Click,
            });
            JumpUtils.jumpToRegistration(deviceDetail, initialParams.deviceId);
          }}
          isLast={false}
          showTip={false}
        />
        <RectView
          // type="manual"
          icon={require('@assets/home/<USER>')}
          title={Strings.getLang(
            'rn_61004_common_detaillist_usermanual_textview_text',
          )}
          isLast
          onPress={() => {
            tracer.click({
              eleid: eleIdMap.Product_Encyclopedia_Button_Click,
            });
            const route = 'ChervonIot://EGO/DeviceManage/productHelp';
            const params = {
              productId: initialParams.productId,
            };
            JumpUtils.jumpTo(route, params);
          }}
          showTip={false}
          disabled={false}
        />
      </View>
      <View style={styles.rects}>
        <RectView
          showTip={showRed}
          // type="upgrade"
          icon={require('@assets/home/<USER>')}
          disabled={disabled}
          title={updateTitle}
          extra={updateValueText}
          onPress={() => {
            tracer.click({
              eleid: eleIdMap.Firmware_Update_Button_Click,
            });
            const {connected} = rootStore.deviceConnectionStore.state;
            /**
             * upgradeModel参数,只要wifi在线，就传0(wifi在线优先级高)，有且仅有蓝牙连接时 传1
             * connected判断wifi是否连接
             */
            if (connected) {
              const url = `ChervonIot://EGO/DeviceManage/OTAInfo?deviceId=${
                initialParams.deviceId
              }&singleMcu=${true}&upgradeModel=${
                UPGRADE_MODEL.WIFI
              }&isSubset=${false}&deviceWifiIsOnline=${connected}`;
              mobile.jumpTo(url, () => {});
            } else {
              const url = `ChervonIot://EGO/DeviceManage/OTAInfo?deviceId=${
                initialParams.deviceId
              }&singleMcu=${true}&upgradeModel=${
                UPGRADE_MODEL.BLUETOOTH
              }&isSubset=${false}&deviceWifiIsOnline=${connected}`;
              mobile.jumpTo(url, () => {});
            }
          }}
          isLast={false}
        />
        <RectView
          icon={require('@assets/common/61004_common_icon_accessories.png')}
          title={Strings.getLang('rn_common_detaillist_parts_textview_text')}
          isLast
          extra={
            <Text style={[styles.active, cTextStyle]} numberOfLines={1}>
              {subTitle}
            </Text>
          }
          onPress={() => {
            tracer.click({
              eleid: eleIdMap.Enclosure_Button_Click,
            });
            jumpToParts({
              productId: initialParams.productId,
              deviceId: initialParams.deviceId,
            });
          }}
          showTip={false}
          disabled={false}
        />
      </View>
    </>
  );
});

export default memo(CardList);

const styles = StyleSheet.create({
  rects: {
    flexDirection: 'row',
    marginTop: 13,
    maxWidth: '100%',
    flexWrap: 'wrap',
  },
  rectsNoTop: {
    flexDirection: 'row',
    maxWidth: '100%',
    flexWrap: 'wrap',
  },
  active: {
    color: '#77BC1F',
    fontSize: 13,
    lineHeight: 16,
    marginTop: 3,
  },
  inactive: {
    fontSize: 13,
    color: '#000000',
    lineHeight: 16,
    marginTop: 3,
  },
});
