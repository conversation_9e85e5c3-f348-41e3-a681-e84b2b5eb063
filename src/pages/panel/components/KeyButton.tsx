/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2023-02-09 11:35:30
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-07-22 15:52:01
 * @FilePath: /61004/src/pages/panel/components/KeyButton
 * @Description: 解锁View
 */
import React, {useState, useEffect} from 'react';
import {StyleSheet, View, TouchableWithoutFeedback} from 'react-native';
import {CVNIcon} from 'cvn-panel-kit';
/**
 * 钥匙卡片
 * @param {Object} store - 注入的store对象
 * @param {Object} style - 自定义样式
 * @param {Function} onPress - 点击事件回调函数
 * @returns {Node} - KeyButton组件
 */
export const KeyButton = ({
  style = {},
  onPress = () => {},
  disabled = false,
  source = require('@assets/key/61004_lock.png'),
}) => {
  const [isLoading, setIsLoading] = useState(false);
  useEffect(() => {
    let timer: string | number | NodeJS.Timeout | undefined;
    if (isLoading) {
      timer = setTimeout(() => {
        setIsLoading(false);
      }, 3000);
    }
    return () => {
      clearTimeout(timer);
    };
  }, [isLoading]);

  const handlePress = () => {
    setIsLoading(true);
    onPress();
  };

  return (
    <View style={[styles.container, style]}>
      <TouchableWithoutFeedback onPress={handlePress} disabled={disabled}>
        <CVNIcon size={75} source={source} />
      </TouchableWithoutFeedback>
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingBack: {
    alignItems: 'center',
    justifyContent: 'center',
  },
});
