/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2023-09-27 10:14:59
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-07-17 15:57:18
 * @FilePath: /61004/src/pages/panel/components/HomeUsageCard
 * @Description: 使用数据卡片
 */
import React, {memo, useEffect} from 'react';
import {observer} from 'mobx-react';
import {StyleSheet, Text, View, TouchableWithoutFeedback} from 'react-native';
import {tracer, eleIdMap, pageIdMap} from '@tracer';
import Strings from '@i18n';
import {CVNIcon} from 'cvn-panel-kit';
import {BaseLineChart} from '@components';
import DeviceCharStore from 'IOTRN/src/mobx/deviceChartStore';
import {ChartDataProps} from 'IOTRN/src/types';
/**
 * 使用历史入口卡片
 * @param {Object} store - 注入的store对象
 * @param {string} title - 标题
 * @param {Object} style - 自定义样式
 * @param {string} iconSource - 图标资源
 * @param {Function} goPage - 跳转页面函数
 * @param {string} totalmowingTime - 总割草时间
 * @returns {Node} - HomeUsageCard组件
 */

interface InjectedStores {
  deviceChartStore: DeviceCharStore;
  title?: string;
  style?: object;
  iconSource?: string;
  goPage: (page: string) => void;
  resWithNew: {[key: string]: boolean | number | string};
  homeChartData: ChartDataProps;
}
const HomeUsageCard = observer(
  ({
    title = Strings.getLang('rn_61004_panelhome_usagehistory_textview_text'),
    style = {},
    iconSource = require('@assets/home/<USER>'),
    goPage = () => {},
    resWithNew,
    deviceChartStore,
    homeChartData,
  }: InjectedStores) => {
    const onPress = () => {
      tracer.click({eleid: eleIdMap.Usage_History_Page_Click});
      tracer.page({pageid: pageIdMap.usageHistory});
      goPage('ViewUsageHistory');
    };
    const subTitle = Strings.getLang(
      'rn_61004_usagehistory_alerttotaltime_textview_text',
    );
    useEffect(() => {
      deviceChartStore.actions.getTotalMowingTime();
    }, [resWithNew, deviceChartStore.actions]);
    return (
      <TouchableWithoutFeedback onPress={onPress}>
        <View style={[styles.container, style]}>
          <View style={styles.rect}>
            <View>
              <View style={styles.topView}>
                <View style={styles.leftBox}>
                  <CVNIcon size={34} source={iconSource} />
                  <View style={styles.titleBox}>
                    <Text style={styles.title}>{title}</Text>
                  </View>
                </View>
                <CVNIcon
                  // style={styles.arrowIcon}
                  source={require('@assets/common/61004_common_icon_item_right.png')}
                  size={20}
                />
              </View>
              <View style={styles.subView}>
                <Text style={styles.subTitle}>{subTitle}</Text>
                <Text style={styles.subValue}>
                  {deviceChartStore.state.totalMowingTime}
                </Text>
              </View>
            </View>
          </View>
          <View style={styles.chartContainerBox}>
            <BaseLineChart
              type="home"
              style={styles.cChart}
              homeChartData={homeChartData}
            />
          </View>
        </View>
      </TouchableWithoutFeedback>
    );
  },
);

export default memo(HomeUsageCard);

const styles = StyleSheet.create({
  container: {
    paddingBottom: 15,
    borderRadius: 10,
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowRadius: 15,
    shadowOpacity: 0.05,
    marginHorizontal: 14,
    overflow: 'hidden',
    backgroundColor: '#ffffff',
  },
  rect: {
    flex: 1,
    backgroundColor: '#fff',
    paddingTop: 13,
    paddingHorizontal: 14,
  },
  topView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  leftBox: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  titleBox: {
    marginLeft: 10,
  },
  title: {
    fontSize: 17,
    fontWeight: '500',
    color: '#3C3936',
  },
  subView: {
    marginTop: 1,
    marginLeft: 39,
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  subTitle: {
    color: '#999999',
    fontSize: 13,
    fontWeight: '400',
  },
  subValue: {
    fontSize: 16,
    fontWeight: '500',
    color: '#77BC1F',
    marginLeft: 5,
  },
  // chart
  chartContainerBox: {
    paddingBottom: 15,
    minHeight: 327 - 73 + 15,
    backgroundColor: '#fff',
    flex: 1,
  },
  cChart: {
    marginTop: 8,
    marginHorizontal: 24,
  },
});
