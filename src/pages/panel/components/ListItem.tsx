/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2023-02-09 11:35:30
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-07-31 15:08:42
 * @FilePath: /61004/src/pages/panel/components/ListItem
 * @Description: 首页ListItem
 */
import React from 'react';
import {
  StyleSheet,
  View,
  TouchableWithoutFeedback,
  Text,
  ViewStyle,
} from 'react-native';
import {CVNIcon, LineView} from 'cvn-panel-kit';
import {Disabled} from '@components/Disabled';
/**
 * 首页列表卡片对应每一项item
 * @param {Object} store - 注入的store对象
 * @param {Object} style - 自定义样式
 * @param {Function} onPress - 点击事件回调函数
 * @param {boolean} hideLine - 是否隐藏分割线
 * @param {string} title - 标题
 * @param {string} source - 图片资源
 * @param {boolean} disabled - 是否禁用
 * @param {string} subTitle - 子标题
 * @param {Object} subTitleStyle - 子标题样式
 * @param {boolean} showRed - 是否显示红点
 * @returns {Node} - ListItem组件
 */

interface ListItemProps {
  style?: ViewStyle;
  onPress?: () => void;
  hideLine?: boolean;
  title?: string;
  source?: string;
  disabled?: boolean;
  subTitle?: string;
  subTitleStyle?: ViewStyle;
  showRed?: boolean;
}

export const ListItem = ({
  style = {},
  onPress = () => {},
  hideLine = false,
  title,
  source,
  disabled,
  subTitle = '',
  subTitleStyle = {},
  showRed = false,
}: ListItemProps) => {
  return (
    <View style={[styles.container, style]}>
      <TouchableWithoutFeedback
        onPress={onPress}
        hitSlop={{top: 10, right: 10, left: 10, bottom: 10}}>
        <View style={styles.itemBox}>
          <View style={styles.leftBox}>
            <CVNIcon source={source} size={34} />
            <Text numberOfLines={1} style={styles.title}>
              {title}
            </Text>
            <Text numberOfLines={1} style={[styles.subTitle, subTitleStyle]}>
              {subTitle}
            </Text>
          </View>
          <View style={styles.rightBox}>
            {showRed ? <View style={styles.redDot} /> : null}
            <CVNIcon
              source={require('@assets/common/61004_common_icon_item_right.png')}
              size={20}
            />
          </View>
        </View>
      </TouchableWithoutFeedback>
      {hideLine ? null : <LineView style={styles.lineView} />}
      {disabled ? <Disabled style={styles.disabledView} /> : null}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: 65,
    paddingHorizontal: 15,
    justifyContent: 'center',
  },
  itemBox: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  leftBox: {
    flexDirection: 'row',
    alignItems: 'center',
    flexShrink: 2,
  },
  title: {
    marginLeft: 10,
    fontSize: 15,
    color: '#000000',
    fontWeight: '500',
  },
  subTitle: {
    marginLeft: 15,
    fontSize: 14,
    color: '#77BC1F',
    fontWeight: '400',
    flexShrink: 2,
  },
  lineView: {
    marginTop: 12,
    marginLeft: 118 / 2 - 15,
  },
  disabledView: {
    opacity: 0.6,
  },
  rightBox: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  redDot: {
    backgroundColor: 'red',
    width: 11,
    height: 11,
    borderRadius: 5.5,
    marginRight: 4,
  },
});
