import React, {useState} from 'react';
import {View, Text, StyleSheet, SafeAreaView} from 'react-native';
import {NewSlider} from './NewSlider';

const SliderTestPage = () => {
  const [sliderValue, setSliderValue] = useState(60);
  const [displayText, setDisplayText] = useState('当前值: 5');

  const handleSliderComplete = (config: {index: number; value: number}) => {
    console.log('滑块完成:', config);
    setSliderValue(config.index * 60);
    setDisplayText(`当前值: ${config.value}`);
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>NewSlider 测试页面</Text>
        
        <Text style={styles.valueText}>{displayText}</Text>
        
        <View style={styles.sliderContainer}>
          <NewSlider
            value={sliderValue}
            onSlidingComplete={handleSliderComplete}
            minimumValue={0}
            maximumValue={300}
            clickThreshold={200}
          />
        </View>
        
        <Text style={styles.infoText}>
          拖动滑块测试防抖功能和自定义实现
        </Text>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 40,
    color: '#333',
  },
  valueText: {
    fontSize: 18,
    textAlign: 'center',
    marginBottom: 30,
    color: '#666',
  },
  sliderContainer: {
    marginVertical: 40,
    paddingHorizontal: 20,
  },
  infoText: {
    fontSize: 14,
    textAlign: 'center',
    color: '#999',
    marginTop: 20,
  },
});

export default SliderTestPage;
