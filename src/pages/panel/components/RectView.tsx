import {
  Image,
  ImageSourcePropType,
  StyleSheet,
  Text,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import React, {isValidElement} from 'react';

interface Props {
  icon: ImageSourcePropType;
  title: string;
  extra?: React.ReactNode | string;
  isLast: boolean;
  showTip: boolean;
  onPress: () => void;
  disabled: boolean;
}
const RectView = ({
  icon,
  title,
  extra,
  isLast,
  showTip = false,
  onPress = () => {},
  disabled,
}: Props) => {
  return (
    <View style={[styles.rect, isLast ? {} : styles.marginRight]}>
      <TouchableWithoutFeedback
        onPress={() => {
          if (!disabled) {
            onPress();
          }
        }}>
        <View>
          {showTip && <View style={styles.tip} />}
          <Image style={styles.img} source={icon} />
          <Text numberOfLines={1} style={styles.rectLabel}>
            {title}
          </Text>
          {isValidElement(extra) ? (
            extra
          ) : (
            <Text numberOfLines={1} style={[styles.rectDesc]}>
              {extra ?? ''}
            </Text>
          )}
        </View>
      </TouchableWithoutFeedback>

      {disabled ? <View style={[styles.disabled]} /> : null}
    </View>
  );
};

export default RectView;
const styles = StyleSheet.create({
  rect: {
    borderRadius: 5,
    flex: 1,
    backgroundColor: '#fff',
    paddingVertical: 12,
    paddingHorizontal: 15,
    position: 'relative',
    height: 100,
  },
  tip: {
    width: 7,
    height: 7,
    borderRadius: 3.5,
    backgroundColor: '#FF4646',
    position: 'absolute',
    top: 0,
    right: 0,
  },
  rectLabel: {
    color: '#000',
    fontSize: 16,
    fontWeight: '500',
    lineHeight: 19,
    marginTop: 6,
  },
  rectDesc: {
    color: '#77BC1F',
    fontSize: 14,
    lineHeight: 16,
    marginTop: 3,
    flexShrink: 2,
  },
  disabled: {
    position: 'absolute',
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
    opacity: 0.4,
    backgroundColor: '#ffffff',
    borderRadius: 5,
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.05,
    shadowColor: '#000000',
    shadowRadius: 7.5,
  },
  img: {
    width: 34,
    height: 34,
    resizeMode: 'contain',
  },
  marginRight: {
    marginRight: 15,
  },
});
