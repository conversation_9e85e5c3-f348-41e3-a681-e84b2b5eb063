import LinearGradient from 'react-native-linear-gradient';
import React, {memo} from 'react';
import {Text, View, StyleSheet, Vibration, Pressable} from 'react-native';
import {useModal} from '@components/Modal';

import Strings from '@i18n';
import {observer} from 'mobx-react';
import {CVNIcon, mobile} from 'cvn-panel-kit';
import {KeyButton} from '@pages/panel/components';
import ModelMap from '@utils/model';
import {tracer, eleIdMap} from '@tracer';
import BatteryStore from 'IOTRN/src/mobx/batteryStore';
import DeviceConnectionStore from 'IOTRN/src/mobx/deviceConnectionStore';
import DeviceConfigurationStore from 'IOTRN/src/mobx/deviceConfigurationStore';
import RightArrow from 'IOTRN/src/assets/svg/61004_svg_rightArrow';

interface InjectedStores {
  batteryManagementStore: BatteryStore;
  deviceConnectionStore: DeviceConnectionStore;
  deviceConfigurationStore: DeviceConfigurationStore;
  disabled: boolean;
  goPage: (page: string) => void;
}

const Dashboard = observer(
  ({
    batteryManagementStore,
    deviceConnectionStore,
    deviceConfigurationStore,
    disabled,
    goPage,
  }: InjectedStores) => {
    const {showModal} = useModal();

    const iconWIFISource = deviceConnectionStore.state.connected
      ? require('@assets/home/<USER>')
      : require('@assets/home/<USER>');
    const iconBleSource = deviceConnectionStore.state.bleConnected
      ? require('@assets/home/<USER>')
      : require('@assets/home/<USER>');
    const disabledLock = require('@assets/key/61004_disablelock.png');

    const lockStatusEnums = {
      keyUnlock: 0, // 物理解锁
      remoteLock: 1, // 远程未解锁
      remoteUnlock: 2, // 远程解锁
    };
    const keySource: {[key: string]: string} = {
      0: require('@assets/key/61004_keylock.png'),
      1: require('@assets/key/61004_lock.png'),
      2: require('@assets/key/61004_unlock.png'),
    };

    const keyButtonOnPress = () => {
      const {lockStatus} = deviceConfigurationStore.actions;
      tracer.click({
        eleid: eleIdMap.Remote_Unlock_Button_Click,
        expand: {mode: lockStatus},
      });
      if (lockStatus === lockStatusEnums.keyUnlock) {
        tracer.page({eleid: eleIdMap.Home_Page_Unlock_Alert_Exposure});
        showModal({
          content: Strings.getLang(
            'rn_61004_panelhome_keylock_description_textview_text',
          ),
          onConfirm: () => {},
        });
        return;
      }
      if (lockStatus === lockStatusEnums.remoteUnlock) {
        tracer.page({eleid: eleIdMap.Home_Page_Unlock_Alert_Exposure});
        showModal({
          content: Strings.getLang(
            'rn_61004_panelhome_remoteunlock_description_textview_text',
          ),
          onConfirm: () => {},
        });
        return;
      }
      Vibration.vibrate(); // 参数为震动持续时间（毫秒）
      mobile.toast(
        Strings.getLang('rn_61004_panelhome_vehicalunlocked_textview_text'),
        () => {},
      );
      const params = {
        [ModelMap.remote_lock]: 2,
      };
      deviceConnectionStore.actions.editProperty({
        propertyData: params,
      });
    };

    const onPress = () => {
      if (disabled) {
        return;
      }
      tracer.click({
        eleid: eleIdMap.View_Battery_Button_Click,
      });
      goPage('ViewBattery');
    };

    return (
      <View style={styles.statistic}>
        <Pressable onPress={onPress} style={[styles.charge, styles.chargeBack]}>
          <LinearGradient
            style={[styles.charge, styles.bg]}
            start={{x: 0, y: 0}}
            end={{x: 0, y: 1}}
            colors={
              disabled ? ['#C1C1C1FF', '#C1C1C100'] : ['#77BC1FFF', '#C1C1C100']
            }
          />
          {/* 剩余电量 */}
          <View style={styles.batterInfoLayout}>
            <View style={styles.batteryTextBox}>
              <View style={styles.batteryInfo}>
                <CVNIcon
                  size={18}
                  style={styles.batteryIcon}
                  source={require('@assets/battery/61004_icon_remaining_battery.png')}
                />
                {/* <Text style={styles.batteryText}>{batery}</Text> */}
                <Text style={styles.batteryText}>
                  {!disabled
                    ? batteryManagementStore.actions.getBatteryPercentage
                    : '--'}
                </Text>
                <Text style={styles.batteryUnit}>%</Text>
              </View>
              <View style={styles.rightArrow}>
                <RightArrow />
              </View>
            </View>
            <Text style={styles.statusText}>
              {Strings.getLang('rn_61004_panelhome_butteryInfo_textview_text')}
            </Text>
          </View>
        </Pressable>
        {/* 开锁功能 */}
        <View style={styles.topRightBox}>
          <View style={styles.connectedView}>
            <CVNIcon
              size={30}
              style={styles.iconStyle}
              source={iconBleSource}
            />
            <CVNIcon size={30} source={iconWIFISource} />
          </View>
          <KeyButton
            style={styles.keyBtn}
            onPress={keyButtonOnPress}
            disabled={disabled}
            source={
              !disabled
                ? keySource[deviceConfigurationStore.actions.lockStatus]
                : disabledLock
            }
          />
        </View>
      </View>
    );
  },
);

export default memo(Dashboard);

const styles = StyleSheet.create({
  statistic: {
    flexDirection: 'row',
  },
  bg: {
    position: 'absolute',
    top: 0,
    left: 0,
    opacity: 0.33,
  },
  charge: {
    width: 170,
    height: 196,
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
    alignItems: 'center',
    paddingVertical: 25,
    position: 'relative',
  },
  chargeBack: {
    marginLeft: 35,
  },
  batteryView: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  batteryIcon: {
    position: 'relative',
    top: -2,
  },
  leftIcon: {
    marginTop: 19,
    width: 58,
    height: 58,
  },
  left: {
    lineHeight: 48,
    color: '#000000',
    fontSize: 40,
    fontWeight: '500',
  },
  connectedView: {
    display: 'flex',
    flexDirection: 'row',
  },
  iconStyle: {
    marginRight: 8,
  },
  keyBtn: {
    marginTop: 25,
  },
  iconBle: {
    marginRight: 5,
  },
  batterInfoLayout: {
    display: 'flex',
    justifyContent: 'space-between',
    alignContent: 'space-between',
  },
  batteryTextBox: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
  },
  batteryInfo: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  batteryText: {
    position: 'relative',
    top: 6,
    marginLeft: 4,
    color: '#000000',
    fontSize: 30,
    fontWeight: '500',
  },
  batteryUnit: {
    marginLeft: 1.5,
    marginTop: 8,
    fontSize: 15,
    color: '#000000',
    fontWeight: '500',
  },
  rightArrow: {
    marginLeft: 17,
  },
  statusText: {
    lineHeight: 18,
    fontSize: 14,
    color: '#666666',
    marginTop: 6.5,
  },

  topRightBox: {
    marginRight: 35,
    flex: 1,
    alignItems: 'flex-end',
  },
  rotationView: {
    marginTop: 12,
  },
});
