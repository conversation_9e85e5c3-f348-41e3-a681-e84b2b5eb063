import {StyleSheet, Text, View, ViewStyle} from 'react-native';
import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {Slider} from 'react-native-elements';
import {CVNIcon} from 'cvn-panel-kit';
import {DEVICE_WIDTH} from '@utils/device';

// 类型定义
interface SliderStepConfig {
  readonly index: number;
  readonly value: number;
}

interface SliderProps {
  readonly value: number;
  readonly onSlidingComplete: (config: SliderStepConfig) => void;
  readonly minimumValue: number;
  readonly maximumValue: number;
  readonly style?: ViewStyle;
  readonly clickThreshold?: number; // 防抖阈值
}

// 常量定义
const SLIDER_CONSTANTS = {
  STEP_SIZE: 60,
  THUMB_SIZE: 20,
  TRACK_HEIGHT: 14,
  TRACK_RADIUS: 7,
  DOT_SIZE: 4,
  DOT_RADIUS: 2,
  VALUE_BUBBLE_HEIGHT: 25,
  VALUE_BUBBLE_WIDTH: 60,
  VALUE_BUBBLE_RADIUS: 12.5,
  DEFAULT_CLICK_THRESHOLD: 200,
  DEFAULT_VALUE_THRESHOLD: 5,
} as const;

const COLORS = {
  PRIMARY: '#77BC1F',
  SECONDARY: '#D8D8D8',
  WHITE: '#fff',
  BLACK: '#000',
  TRANSPARENT: 'transparent',
} as const;

// 预定义的步进值配置
const STEP_VALUES = [0, 5, 15, 30, 100, 300] as const;

/**
 * 创建步进配置映射
 */
const createStepConfig = (): Record<number, SliderStepConfig> => {
  const config: Record<number, SliderStepConfig> = {};
  STEP_VALUES.forEach((stepValue, index) => {
    const sliderPosition = index * SLIDER_CONSTANTS.STEP_SIZE;
    config[sliderPosition] = {
      index,
      value: stepValue,
    };
  });
  return config;
};

/**
 * 计算圆点位置
 */
const calculateDotPosition = (index: number): number => {
  const marginX = (DEVICE_WIDTH - 83 - 6 * 4) / 5;
  return 9 + index * (4 + marginX);
};

export const NewSlider = ({
  value,
  onSlidingComplete,
  minimumValue,
  maximumValue,
  style = {},
  clickThreshold = SLIDER_CONSTANTS.DEFAULT_CLICK_THRESHOLD,
}: SliderProps) => {
  // 状态管理
  const [displayValue, setDisplayValue] = useState<number>(0);

  // 防抖相关的 refs
  const startTimeRef = useRef<number>(0);
  const lastValueRef = useRef<number>(value);

  // 步进配置 - 使用 useMemo 优化性能
  const stepConfig = useMemo(() => createStepConfig(), []);

  // 根据滑块位置获取显示值
  const getDisplayValueFromSliderPosition = useCallback(
    (sliderValue: number): number => {
      return stepConfig[sliderValue]?.value ?? 0;
    },
    [stepConfig],
  );

  // 初始化显示值
  useEffect(() => {
    console.log('====================================');
    console.log('useEffect', value);
    console.log('====================================');
    const initialDisplayValue = getDisplayValueFromSliderPosition(value);
    setDisplayValue(initialDisplayValue);
    lastValueRef.current = value;
  }, [value, getDisplayValueFromSliderPosition]);
  // 防抖处理的滑动开始事件
  const handleSlidingStart = () => {
    startTimeRef.current = Date.now();
    console.log('====================================');
    console.log('handleSlidingStart: ', startTimeRef.current);
    console.log('====================================');
  };

  // 防抖处理的值变化事件
  const handleValueChange = (newValue: number) => {
    const now = Date.now();
    const timeDiff = now - startTimeRef.current;

    // 仅当时间超过阈值（非快速点击）或值变化较大时触发
    if (startTimeRef.current !== 0 && timeDiff > clickThreshold) {
      console.log('====================================');
      console.log(timeDiff, 'timeDiff');
      console.log('====================================');
      console.log('====================================');
      console.log('newValue:', newValue);
      console.log('====================================');
      const newDisplayValue = getDisplayValueFromSliderPosition(newValue);
      setDisplayValue(newDisplayValue);
    }
  };

  // 滑动完成事件处理
  const handleSlidingComplete = useCallback(
    (sliderValue: number) => {
      const now = Date.now();
      const timeDiff = now - startTimeRef.current;
      const config = stepConfig[sliderValue];
      console.log('====================================');
      console.log(
        sliderValue,
        'sliderValue',
        timeDiff,
        'timeDiff',
        config,
        startTimeRef.current,
      );
      console.log('====================================');
      if (config && startTimeRef.current !== 0 && timeDiff > clickThreshold) {
        console.log('====================================');
        console.log('newValue:', config.value);
        console.log('====================================');
        startTimeRef.current = 0;
        onSlidingComplete(config);
        setDisplayValue(config.value);
      }
    },
    [stepConfig, clickThreshold, onSlidingComplete],
  );

  // 渲染值显示气泡
  const renderValueBubble = useCallback(() => {
    if (displayValue <= minimumValue || displayValue >= maximumValue) {
      return null;
    }

    return (
      <View style={styles.valueBubble}>
        <Text style={styles.valueBubbleText}>{displayValue}</Text>
      </View>
    );
  }, [displayValue, minimumValue, maximumValue]);

  // 渲染滑块拇指
  const renderThumb = useCallback(
    () => (
      <View style={styles.thumbContainer}>
        <CVNIcon
          size={33}
          source={require('@assets/home/<USER>')}
        />
        {renderValueBubble()}
      </View>
    ),
    [renderValueBubble],
  );

  // 渲染指示点
  const renderIndicatorDots = useCallback(() => {
    return STEP_VALUES.map((stepValue, index) => {
      const dotPosition = calculateDotPosition(index);
      const isActive = displayValue === stepValue;

      return (
        <View
          key={stepValue}
          style={[
            styles.indicatorDot,
            {
              left: dotPosition,
              backgroundColor: isActive ? COLORS.TRANSPARENT : COLORS.WHITE,
            },
          ]}
        />
      );
    });
  }, [displayValue]);

  return (
    <View style={[styles.wrapper, style]}>
      <Text style={styles.minLabel}>{minimumValue}</Text>
      <Text style={styles.maxLabel}>{maximumValue}</Text>

      <Slider
        value={value}
        onSlidingStart={handleSlidingStart}
        onValueChange={handleValueChange}
        onSlidingComplete={handleSlidingComplete}
        style={styles.slider}
        minimumValue={minimumValue}
        maximumValue={maximumValue}
        step={SLIDER_CONSTANTS.STEP_SIZE}
        thumbTouchSize={{
          width: SLIDER_CONSTANTS.THUMB_SIZE,
          height: SLIDER_CONSTANTS.THUMB_SIZE,
        }}
        minimumTrackTintColor={COLORS.PRIMARY}
        maximumTrackTintColor={COLORS.SECONDARY}
        trackStyle={styles.track}
        thumbStyle={styles.thumb}
        thumbProps={{
          children: renderThumb(),
        }}
      />

      {renderIndicatorDots()}
    </View>
  );
};

// 优化后的样式定义
export const styles = StyleSheet.create({
  wrapper: {
    position: 'relative',
    flexDirection: 'row',
    height: 60,
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
  },
  minLabel: {
    position: 'absolute',
    left: 0,
    top: 17.5,
    fontSize: 12,
    color: COLORS.BLACK,
  },
  maxLabel: {
    position: 'absolute',
    right: 0,
    top: 17.5,
    fontSize: 12,
    color: COLORS.BLACK,
  },
  slider: {
    flex: 1,
    height: 33,
  },
  track: {
    height: SLIDER_CONSTANTS.TRACK_HEIGHT,
    borderRadius: SLIDER_CONSTANTS.TRACK_RADIUS,
    backgroundColor: COLORS.SECONDARY,
  },
  thumb: {
    height: SLIDER_CONSTANTS.THUMB_SIZE,
    width: SLIDER_CONSTANTS.THUMB_SIZE,
    backgroundColor: COLORS.TRANSPARENT,
  },
  thumbContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    bottom: 2,
  },
  valueBubble: {
    position: 'absolute',
    backgroundColor: COLORS.PRIMARY,
    height: SLIDER_CONSTANTS.VALUE_BUBBLE_HEIGHT,
    width: SLIDER_CONSTANTS.VALUE_BUBBLE_WIDTH,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: SLIDER_CONSTANTS.VALUE_BUBBLE_RADIUS,
    top: -32,
    left: -20,
  },
  valueBubbleText: {
    color: COLORS.WHITE,
    fontSize: 16,
    fontWeight: 'bold',
  },
  indicatorDot: {
    width: SLIDER_CONSTANTS.DOT_SIZE,
    height: SLIDER_CONSTANTS.DOT_SIZE,
    borderRadius: SLIDER_CONSTANTS.DOT_RADIUS,
    position: 'absolute',
    bottom: 14,
    backgroundColor: COLORS.WHITE,
  },
});
