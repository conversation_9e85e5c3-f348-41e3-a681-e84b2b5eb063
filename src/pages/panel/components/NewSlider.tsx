/* eslint-disable react-hooks/exhaustive-deps */
import {
  StyleSheet,
  Text,
  View,
  ViewStyle,
  PanResponder,
  Animated,
} from 'react-native';
import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {CVNIcon} from 'cvn-panel-kit';
import {DEVICE_WIDTH} from 'IOTRN/src/utils/device';

/**
 * 高级自定义 Slider 组件
 * 基于 React Native 原生 API 实现
 * 遵循高级开发最佳实践
 */
interface CustomSliderProps {
  readonly value: number;
  readonly onSlidingComplete: (value: {index: number; value: number}) => void;
  readonly minimumValue: number;
  readonly maximumValue: number;
  readonly style?: ViewStyle;
  readonly disabled?: boolean;
}

// 设计系统常量
const DESIGN_TOKENS = {
  // 尺寸规范
  TRACK_HEIGHT: 14,
  TRACK_RADIUS: 7,
  THUMB_SIZE: 33,
  THUMB_TOUCH_AREA: 44, // iOS HIG 推荐最小触摸区域
  DOT_SIZE: 4,
  DOT_RADIUS: 2,

  // 动画参数
  SPRING_TENSION: 300,
  SPRING_FRICTION: 30,
  SCALE_ACTIVE: 1.2,
  SCALE_NORMAL: 1.0,

  // 交互参数
  STEP_SIZE: 60,

  // 气泡样式
  BUBBLE_HEIGHT: 25,
  BUBBLE_WIDTH: 60,
  BUBBLE_RADIUS: 12.5,
  BUBBLE_OFFSET: -30,

  // 布局参数
  CONTAINER_HEIGHT: 60,
  LABEL_OFFSET: 16,
  DOT_BOTTOM_OFFSET: 14,
} as const;

// 主题色彩
const THEME_COLORS = {
  PRIMARY: '#77BC1F',
  SECONDARY: '#D8D8D8',
  WHITE: '#FFFFFF',
  BLACK: '#000000',
  GRAY_TEXT: '#999999',
  SHADOW: 'rgba(0, 0, 0, 0.15)',
  TRANSPARENT: 'transparent',
} as const;

// 业务配置 - 步进值
const STEP_VALUES = [0, 5, 15, 30, 100, 300] as const;

// 工具函数 - 高级开发实践

/**
 * 将滑块位置转换为业务值
 * @param position 滑块位置 (0-300)
 * @param trackWidth 轨道宽度
 * @param minValue 最小值
 * @param maxValue 最大值
 * @returns 对应的业务值
 */
const positionToValue = (
  position: number,
  trackWidth: number,
  minValue: number,
  maxValue: number,
): number => {
  const ratio = Math.max(
    0,
    Math.min(1, position / (trackWidth - DESIGN_TOKENS.THUMB_SIZE)),
  );
  return minValue + ratio * (maxValue - minValue);
};
/**
 * 将值对齐到最近的步进值
 * @param value 当前值
 * @param stepSize 步进大小
 * @param minValue 最小值
 * @returns 对齐后的值
 */
const snapToStep = (
  value: number,
  stepSize: number,
  minValue: number,
): number => {
  const steps = Math.round((value - minValue) / stepSize);
  return minValue + steps * stepSize;
};

/**
 * 高级自定义 Slider 组件实现
 */
export const NewSlider = ({
  value,
  onSlidingComplete,
  minimumValue,
  maximumValue,
  style = {},
  disabled = false,
}: CustomSliderProps) => {
  // 状态管理
  const [displayValue, setDisplayValue] = useState<number>(0);
  const [temp, setTemp] = useState(value); // 暂存值
  // 动画值
  const translateX = useRef(new Animated.Value(0)).current;
  const scale = useRef(new Animated.Value(DESIGN_TOKENS.SCALE_NORMAL)).current;
  const currentValueRef = useRef<number>(value);
  const stepSizeRef = useRef<number>(DESIGN_TOKENS.STEP_SIZE);

  // 引用值
  const trackWidthRef = useRef<number>(DEVICE_WIDTH - 58); // 轨道宽度

  /**
   * 将 [0,1] 范围内的数值映射到 [0,5] 范围内的整数
   * @param num 原数值（0 ≤ num ≤ 1）
   * @returns 目标整数（0 ≤ result ≤ 5）
   */
  const mapValue = (num: number) => {
    // 限制输入范围在 [0,1]
    const clamped = Math.max(0, Math.min(1, num));
    // 映射到 [0,5] 并取整（四舍五入确保精度）
    return Math.round(clamped * 5);
  };

  // 初始化
  useEffect(() => {
    const initalValue =
      Math.min(value / (STEP_VALUES.length - 1), 1) * trackWidthRef.current;
    stepSizeRef.current = trackWidthRef.current / (STEP_VALUES.length - 1);
    if (value === temp) {
      setDisplayValue(initalValue);
      translateX.setValue(initalValue); // 初始化动画值
      setTemp(value); // 初始化显示值
    }
  }, [value]);
  // PanResponder 手势处理器
  const panResponder = useMemo(
    () =>
      PanResponder.create({
        onStartShouldSetPanResponder: () => !disabled,
        onMoveShouldSetPanResponder: () => !disabled,

        onPanResponderGrant: () => {
          // 拇指缩放动画
          Animated.spring(scale, {
            toValue: DESIGN_TOKENS.SCALE_ACTIVE,
            tension: DESIGN_TOKENS.SPRING_TENSION,
            friction: DESIGN_TOKENS.SPRING_FRICTION,
            useNativeDriver: true,
          }).start();
        },
        onPanResponderMove: (_, gestureState) => {
          const newPosition = Math.max(
            0,
            Math.min(
              trackWidthRef.current - DESIGN_TOKENS.THUMB_SIZE,
              currentValueRef.current + gestureState.dx,
            ),
          );

          // 转换为业务值
          const newValue = positionToValue(
            newPosition,
            trackWidthRef.current,
            minimumValue,
            maximumValue,
          );
          // 对齐到步进值
          const snappedValue = snapToStep(
            newValue,
            stepSizeRef.current,
            minimumValue,
          );
          const index = mapValue(snappedValue / trackWidthRef.current);
          setTemp(index);
          // 更新动画值
          translateX.setValue(snappedValue);
          setDisplayValue(snappedValue);
        },
        onPanResponderRelease: (_, gestureState) => {
          currentValueRef.current = gestureState.dx + currentValueRef.current;
          // 恢复拇指大小
          Animated.spring(scale, {
            toValue: DESIGN_TOKENS.SCALE_NORMAL,
            tension: DESIGN_TOKENS.SPRING_TENSION,
            friction: DESIGN_TOKENS.SPRING_FRICTION,
            useNativeDriver: true,
          }).start();
          const finalValue = positionToValue(
            currentValueRef.current,
            trackWidthRef.current,
            minimumValue,
            maximumValue,
          );

          const snappedValue = snapToStep(
            finalValue,
            stepSizeRef.current,
            minimumValue,
          );

          const index = mapValue(snappedValue / trackWidthRef.current);
          onSlidingComplete({value: STEP_VALUES[index], index: index});
        },
      }),
    [
      disabled,
      scale,
      minimumValue,
      maximumValue,
      translateX,
      onSlidingComplete,
    ],
  );

  /**
   * 计算指示点位置
   * @param index 点的索引
   * @returns 点的 left 位置
   */
  const calculateDotPosition = (index: number): number => {
    const totalDots = STEP_VALUES.length;
    const availableWidth =
      trackWidthRef.current - totalDots * DESIGN_TOKENS.DOT_SIZE;
    const spacing = availableWidth / (totalDots - 1);
    return 9 + index * spacing;
  };

  // 动画样式
  const thumbAnimatedStyle = useMemo(
    () => ({
      transform: [{translateX: translateX}, {scale: scale}],
    }),
    [translateX, scale],
  );

  // 渲染值显示气泡
  const renderValueBubble = useCallback(() => {
    const displayNewValue = STEP_VALUES[temp];
    if (displayNewValue <= minimumValue || displayNewValue >= maximumValue) {
      return null;
    }

    return (
      <View style={styles.valueBubble}>
        <Text style={styles.valueBubbleText}>{displayNewValue}</Text>
      </View>
    );
  }, [temp, minimumValue, maximumValue]);

  // 渲染滑块拇指
  const renderThumb = useCallback(
    () => (
      <View style={styles.thumbContainer}>
        <CVNIcon
          size={33}
          source={require('@assets/home/<USER>')}
        />
        {renderValueBubble()}
      </View>
    ),
    [renderValueBubble],
  );

  // 渲染指示点
  const renderIndicatorDots = useCallback(() => {
    return STEP_VALUES.map((stepValue, index) => {
      const dotPosition = calculateDotPosition(index);
      const isActive = temp === index;

      return (
        <View
          key={stepValue}
          style={[
            styles.indicatorDot,
            {
              left: dotPosition,
              backgroundColor: isActive
                ? THEME_COLORS.TRANSPARENT
                : THEME_COLORS.WHITE,
            },
          ]}
        />
      );
    });
  }, [temp]);
  return (
    <View style={[styles.wrapper, style]}>
      <Text style={styles.minLabel}>{minimumValue}</Text>
      <Text style={styles.maxLabel}>{maximumValue}</Text>

      {/* 自定义滑块组件 */}
      <View
        style={styles.sliderContainer}
        onLayout={event => {
          const {width} = event.nativeEvent.layout;
          trackWidthRef.current = width;
          stepSizeRef.current =
            trackWidthRef.current / (STEP_VALUES.length - 1);
        }}>
        {/* 背景轨道 */}
        <View style={styles.track} />

        {/* 活动轨道 - 使用简单的 View 避免动画问题 */}
        <View style={[styles.activeTrack, {width: displayValue}]} />

        {/* 拇指 */}
        <Animated.View
          style={[styles.thumbWrapper, thumbAnimatedStyle]}
          {...panResponder.panHandlers}>
          {renderThumb()}
        </Animated.View>
      </View>

      {renderIndicatorDots()}
    </View>
  );
};

// 高级样式系统 - 基于设计令牌
export const styles = StyleSheet.create({
  wrapper: {
    flexDirection: 'row',
    height: DESIGN_TOKENS.CONTAINER_HEIGHT,
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
  },
  minLabel: {
    position: 'absolute',
    left: 0,
    top: DESIGN_TOKENS.LABEL_OFFSET,
    fontSize: 12,
    color: THEME_COLORS.BLACK,
  },
  maxLabel: {
    position: 'absolute',
    right: 0,
    top: DESIGN_TOKENS.LABEL_OFFSET,
    fontSize: 12,
    color: THEME_COLORS.BLACK,
  },
  sliderContainer: {
    flex: 1,
    height: 33,
    justifyContent: 'center',
  },
  track: {
    position: 'absolute',
    left: 0,
    right: 0,
    height: DESIGN_TOKENS.TRACK_HEIGHT,
    borderRadius: DESIGN_TOKENS.TRACK_RADIUS,
    backgroundColor: THEME_COLORS.SECONDARY,
  },
  activeTrack: {
    position: 'absolute',
    left: 0,
    height: DESIGN_TOKENS.TRACK_HEIGHT,
    borderRadius: DESIGN_TOKENS.TRACK_RADIUS,
    backgroundColor: THEME_COLORS.PRIMARY,
  },
  thumbWrapper: {
    position: 'absolute',
    width: DESIGN_TOKENS.THUMB_TOUCH_AREA,
    height: DESIGN_TOKENS.THUMB_TOUCH_AREA,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: -DESIGN_TOKENS.THUMB_TOUCH_AREA / 2,
  },
  thumbContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  valueBubble: {
    position: 'absolute',
    backgroundColor: THEME_COLORS.PRIMARY,
    height: DESIGN_TOKENS.BUBBLE_HEIGHT,
    width: DESIGN_TOKENS.BUBBLE_WIDTH,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: DESIGN_TOKENS.BUBBLE_RADIUS,
    top: DESIGN_TOKENS.BUBBLE_OFFSET,
    left: -DESIGN_TOKENS.BUBBLE_WIDTH / 4,
    shadowColor: THEME_COLORS.SHADOW,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  valueBubbleText: {
    color: THEME_COLORS.WHITE,
    fontSize: 16,
    fontWeight: 'bold',
  },
  indicatorDot: {
    width: DESIGN_TOKENS.DOT_SIZE,
    height: DESIGN_TOKENS.DOT_SIZE,
    borderRadius: DESIGN_TOKENS.DOT_RADIUS,
    position: 'absolute',
    bottom: DESIGN_TOKENS.DOT_BOTTOM_OFFSET,
    backgroundColor: THEME_COLORS.WHITE,
  },
});
