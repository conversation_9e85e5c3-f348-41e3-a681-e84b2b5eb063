import {
  StyleSheet,
  Text,
  View,
  ViewStyle,
  PanResponder,
  Animated,
} from 'react-native';
import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {CVNIcon} from 'cvn-panel-kit';
import {DEVICE_WIDTH} from '@utils/device';

// 类型定义
interface SliderStepConfig {
  readonly index: number;
  readonly value: number;
}

interface SliderProps {
  readonly value: number;
  readonly onSlidingComplete: (config: SliderStepConfig) => void;
  readonly minimumValue: number;
  readonly maximumValue: number;
  readonly style?: ViewStyle;
  readonly clickThreshold?: number; // 防抖阈值
}

// 常量定义
const SLIDER_CONSTANTS = {
  STEP_SIZE: 60,
  THUMB_SIZE: 20,
  TRACK_HEIGHT: 14,
  TRACK_RADIUS: 7,
  DOT_SIZE: 4,
  DOT_RADIUS: 2,
  VALUE_BUBBLE_HEIGHT: 25,
  VALUE_BUBBLE_WIDTH: 60,
  VALUE_BUBBLE_RADIUS: 12.5,
  DEFAULT_CLICK_THRESHOLD: 200,
  DEFAULT_VALUE_THRESHOLD: 5,
  TRACK_MARGIN: 40, // 轨道左右边距
  THUMB_TOUCH_SIZE: 40, // 拇指触摸区域大小
} as const;

const COLORS = {
  PRIMARY: '#77BC1F',
  SECONDARY: '#D8D8D8',
  WHITE: '#fff',
  BLACK: '#000',
  TRANSPARENT: 'transparent',
} as const;

// 预定义的步进值配置
const STEP_VALUES = [0, 5, 15, 30, 100, 300] as const;

/**
 * 创建步进配置映射
 */
const createStepConfig = (): Record<number, SliderStepConfig> => {
  const config: Record<number, SliderStepConfig> = {};
  STEP_VALUES.forEach((stepValue, index) => {
    const sliderPosition = index * SLIDER_CONSTANTS.STEP_SIZE;
    config[sliderPosition] = {
      index,
      value: stepValue,
    };
  });
  return config;
};

/**
 * 计算圆点位置
 */
const calculateDotPosition = (index: number): number => {
  const marginX = (DEVICE_WIDTH - 83 - 6 * 4) / 5;
  return 9 + index * (4 + marginX);
};

/**
 * 计算滑块轨道宽度
 */
const getTrackWidth = (): number => {
  return DEVICE_WIDTH - SLIDER_CONSTANTS.TRACK_MARGIN * 2;
};

/**
 * 将位置转换为值
 */
const positionToValue = (
  position: number,
  trackWidth: number,
  minimumValue: number,
  maximumValue: number,
): number => {
  'worklet';
  const ratio = position / trackWidth;
  const range = maximumValue - minimumValue;
  return minimumValue + ratio * range;
};

/**
 * 将值转换为位置
 */
const valueToPosition = (
  value: number,
  trackWidth: number,
  minimumValue: number,
  maximumValue: number,
): number => {
  'worklet';
  const ratio = (value - minimumValue) / (maximumValue - minimumValue);
  return ratio * trackWidth;
};

/**
 * 将值对齐到最近的步进值
 */
const snapToStep = (
  value: number,
  step: number,
  minimumValue: number,
): number => {
  'worklet';
  const steps = Math.round((value - minimumValue) / step);
  return minimumValue + steps * step;
};

/**
 * 获取步进配置 - JS 版本
 */
const getStepConfigJS = (
  sliderValue: number,
): {index: number; value: number} | null => {
  const stepValues = [0, 5, 15, 30, 100, 300];
  const stepSize = 60;

  // 找到对应的索引
  const index = Math.round(sliderValue / stepSize);
  if (index >= 0 && index < stepValues.length) {
    return {
      index,
      value: stepValues[index],
    };
  }
  return null;
};

export const NewSlider = ({
  value,
  onSlidingComplete,
  minimumValue,
  maximumValue,
  style = {},
  clickThreshold = SLIDER_CONSTANTS.DEFAULT_CLICK_THRESHOLD,
}: SliderProps) => {
  // 状态管理
  const [displayValue, setDisplayValue] = useState<number>(0);
  const [isDragging, setIsDragging] = useState<boolean>(false);

  // 防抖相关的 refs
  const startTimeRef = useRef<number>(0);
  const lastValueRef = useRef<number>(value);
  const trackWidthRef = useRef<number>(getTrackWidth());

  // 动画值
  const translateX = useRef(new Animated.Value(0)).current;
  const scale = useRef(new Animated.Value(1)).current;

  // 步进配置 - 使用 useMemo 优化性能
  const stepConfig = useMemo(() => createStepConfig(), []);

  // 根据滑块位置获取显示值
  const getDisplayValueFromSliderPosition = useCallback(
    (sliderValue: number): number => {
      return stepConfig[sliderValue]?.value ?? 0;
    },
    [stepConfig],
  );

  // 初始化滑块位置
  useEffect(() => {
    const initialDisplayValue = getDisplayValueFromSliderPosition(value);
    setDisplayValue(initialDisplayValue);
    lastValueRef.current = value;

    // 设置初始位置
    const initialPosition = valueToPosition(
      value,
      trackWidthRef.current,
      minimumValue,
      maximumValue,
    );
    translateX.setValue(initialPosition);
  }, [
    value,
    getDisplayValueFromSliderPosition,
    minimumValue,
    maximumValue,
    translateX,
  ]);

  // 当前位置引用
  const currentPositionRef = useRef(0);

  // PanResponder 手势处理器
  const panResponder = useMemo(
    () =>
      PanResponder.create({
        onStartShouldSetPanResponder: () => true,
        onMoveShouldSetPanResponder: () => true,

        onPanResponderGrant: () => {
          setIsDragging(true);
          startTimeRef.current = Date.now();

          // 缩放动画
          Animated.spring(scale, {
            toValue: 1.2,
            useNativeDriver: true,
          }).start();
        },

        onPanResponderMove: (_, gestureState) => {
          const newPosition = Math.max(
            0,
            Math.min(
              trackWidthRef.current,
              currentPositionRef.current + gestureState.dx,
            ),
          );

          const newValue = positionToValue(
            newPosition,
            trackWidthRef.current,
            minimumValue,
            maximumValue,
          );

          const snappedValue = snapToStep(
            newValue,
            SLIDER_CONSTANTS.STEP_SIZE,
            minimumValue,
          );

          const snappedPosition = valueToPosition(
            snappedValue,
            trackWidthRef.current,
            minimumValue,
            maximumValue,
          );

          translateX.setValue(snappedPosition);

          // 更新显示值 - 防抖处理
          const now = Date.now();
          const timeDiff = now - startTimeRef.current;
          if (timeDiff > clickThreshold) {
            const config = getStepConfigJS(snappedValue);
            if (config) {
              setDisplayValue(config.value);
            }
          }
        },

        onPanResponderRelease: () => {
          setIsDragging(false);

          // 恢复缩放
          Animated.spring(scale, {
            toValue: 1,
            useNativeDriver: true,
          }).start();

          // 获取当前位置并处理完成事件
          translateX.addListener(({value: currentValue}) => {
            const finalValue = positionToValue(
              currentValue,
              trackWidthRef.current,
              minimumValue,
              maximumValue,
            );

            const snappedValue = snapToStep(
              finalValue,
              SLIDER_CONSTANTS.STEP_SIZE,
              minimumValue,
            );

            const config = getStepConfigJS(snappedValue);
            if (config) {
              const now = Date.now();
              const timeDiff = now - startTimeRef.current;
              if (timeDiff > clickThreshold) {
                onSlidingComplete(config);
                setDisplayValue(config.value);
              }
            }
            startTimeRef.current = 0;
            currentPositionRef.current = currentValue;

            // 移除监听器
            translateX.removeAllListeners();
          });
        },
      }),
    [
      clickThreshold,
      minimumValue,
      maximumValue,
      onSlidingComplete,
      translateX,
      scale,
    ],
  );

  // 拇指动画样式
  const thumbAnimatedStyle = useMemo(
    () => ({
      transform: [{translateX: translateX}, {scale: scale}],
    }),
    [translateX, scale],
  );

  // 活动轨道动画样式
  const activeTrackAnimatedStyle = useMemo(
    () => ({
      width: translateX,
    }),
    [translateX],
  );

  // 渲染值显示气泡
  const renderValueBubble = useCallback(() => {
    if (displayValue <= minimumValue || displayValue >= maximumValue) {
      return null;
    }

    return (
      <View style={styles.valueBubble}>
        <Text style={styles.valueBubbleText}>{displayValue}</Text>
      </View>
    );
  }, [displayValue, minimumValue, maximumValue]);

  // 渲染自定义滑块
  const renderCustomSlider = useCallback(() => {
    return (
      <View style={styles.sliderContainer}>
        {/* 背景轨道 */}
        <View style={styles.track} />

        {/* 活动轨道 */}
        <Animated.View style={[styles.activeTrack, activeTrackAnimatedStyle]} />

        {/* 拇指 */}
        <Animated.View
          style={[styles.thumbWrapper, thumbAnimatedStyle]}
          {...panResponder.panHandlers}>
          <View style={styles.thumbContainer}>
            <CVNIcon
              size={33}
              source={require('@assets/home/<USER>')}
            />
            {isDragging && renderValueBubble()}
          </View>
        </Animated.View>
      </View>
    );
  }, [
    panResponder.panHandlers,
    thumbAnimatedStyle,
    activeTrackAnimatedStyle,
    isDragging,
    renderValueBubble,
  ]);

  // 渲染指示点
  const renderIndicatorDots = useCallback(() => {
    return STEP_VALUES.map((stepValue, index) => {
      const dotPosition = calculateDotPosition(index);
      const isActive = displayValue === stepValue;

      return (
        <View
          key={stepValue}
          style={[
            styles.indicatorDot,
            {
              left: dotPosition,
              backgroundColor: isActive ? COLORS.TRANSPARENT : COLORS.WHITE,
            },
          ]}
        />
      );
    });
  }, [displayValue]);

  return (
    <View style={[styles.wrapper, style]}>
      <Text style={styles.minLabel}>{minimumValue}</Text>
      <Text style={styles.maxLabel}>{maximumValue}</Text>

      {renderCustomSlider()}
      {renderIndicatorDots()}
    </View>
  );
};

// 优化后的样式定义
export const styles = StyleSheet.create({
  wrapper: {
    position: 'relative',
    flexDirection: 'row',
    height: 60,
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
  },
  minLabel: {
    position: 'absolute',
    left: 0,
    top: 17.5,
    fontSize: 12,
    color: COLORS.BLACK,
  },
  maxLabel: {
    position: 'absolute',
    right: 0,
    top: 17.5,
    fontSize: 12,
    color: COLORS.BLACK,
  },
  sliderContainer: {
    flex: 1,
    height: 33,
    justifyContent: 'center',
    marginHorizontal: SLIDER_CONSTANTS.TRACK_MARGIN / 2,
  },
  track: {
    position: 'absolute',
    left: 0,
    right: 0,
    height: SLIDER_CONSTANTS.TRACK_HEIGHT,
    borderRadius: SLIDER_CONSTANTS.TRACK_RADIUS,
    backgroundColor: COLORS.SECONDARY,
  },
  activeTrack: {
    position: 'absolute',
    left: 0,
    height: SLIDER_CONSTANTS.TRACK_HEIGHT,
    borderRadius: SLIDER_CONSTANTS.TRACK_RADIUS,
    backgroundColor: COLORS.PRIMARY,
  },
  thumbWrapper: {
    position: 'absolute',
    width: SLIDER_CONSTANTS.THUMB_TOUCH_SIZE,
    height: SLIDER_CONSTANTS.THUMB_TOUCH_SIZE,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: -SLIDER_CONSTANTS.THUMB_TOUCH_SIZE / 2,
  },
  thumbContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  valueBubble: {
    position: 'absolute',
    backgroundColor: COLORS.PRIMARY,
    height: SLIDER_CONSTANTS.VALUE_BUBBLE_HEIGHT,
    width: SLIDER_CONSTANTS.VALUE_BUBBLE_WIDTH,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: SLIDER_CONSTANTS.VALUE_BUBBLE_RADIUS,
    top: -40,
    left: -SLIDER_CONSTANTS.VALUE_BUBBLE_WIDTH / 2,
  },
  valueBubbleText: {
    color: COLORS.WHITE,
    fontSize: 16,
    fontWeight: 'bold',
  },
  indicatorDot: {
    width: SLIDER_CONSTANTS.DOT_SIZE,
    height: SLIDER_CONSTANTS.DOT_SIZE,
    borderRadius: SLIDER_CONSTANTS.DOT_RADIUS,
    position: 'absolute',
    bottom: 14,
    backgroundColor: COLORS.WHITE,
  },
});
