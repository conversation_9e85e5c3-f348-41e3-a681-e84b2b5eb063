import {StyleSheet, Text, View, ViewStyle} from 'react-native';
import React, {useEffect, useMemo, useState} from 'react';
import {Slider} from 'react-native-elements';
import {CVNIcon} from 'cvn-panel-kit';
import {DEVICE_WIDTH} from '@utils/device';
interface Props {
  value: number;
  onSlidingComplete: (v: {value: number; index: number}) => void;
  minimumValue: number;
  maximumValue: number;
  style?: ViewStyle;
}

export const NewSlider = ({
  value,
  onSlidingComplete,
  minimumValue,
  maximumValue,
  style = {},
}: Props) => {
  const [temp, setTemp] = useState(value);

  const stepNumConfig: {[key: string]: {index: number; value: number}} =
    useMemo(
      () => ({
        0: {
          index: 0,
          value: 0,
        },
        60: {
          index: 1,
          value: 5,
        },
        120: {
          index: 2,
          value: 15,
        },
        180: {
          index: 3,
          value: 30,
        },
        240: {
          index: 4,
          value: 100,
        },
        300: {
          index: 5,
          value: 300,
        },
      }),
      [],
    );
  useEffect(() => {
    const selectedValue = stepNumConfig[value]?.value;
    setTemp(selectedValue);
  }, [value, stepNumConfig]);
  return (
    <View style={[styles.wrapper, style]}>
      <Text style={styles.min}>{minimumValue}</Text>
      <Text style={styles.max}>{maximumValue}</Text>
      <Slider
        onSlidingComplete={(...rest) => {
          const selectedValue = stepNumConfig[rest[0]]?.value;
          setTemp(selectedValue);
          onSlidingComplete(stepNumConfig[rest[0]]);
        }}
        style={styles.sliderStyle}
        value={value}
        onValueChange={v => {
          const selectedValue = stepNumConfig[v]?.value;
          setTemp(selectedValue);
        }}
        minimumValue={minimumValue}
        maximumValue={maximumValue}
        thumbTouchSize={{width: 20, height: 20}}
        minimumTrackTintColor="#77BC1F"
        maximumTrackTintColor="#D8D8D8"
        trackStyle={styles.track}
        thumbStyle={styles.thumbStyle}
        thumbProps={{
          children: (
            <View style={styles.outer}>
              <CVNIcon
                size={33}
                source={require('@assets/home/<USER>')}
              />
              {temp > minimumValue && temp < maximumValue ? (
                <View style={styles.val}>
                  <Text style={styles.textStyle}>{temp}</Text>
                </View>
              ) : null}
            </View>
          ),
        }}
        step={60}
      />
      {/* 经过试验，是没办法坐上小圆点的，层级不对，暂不做 */}
      {[0, 5, 15, 30, 100, 300].map((item, index) => {
        const marginX = (DEVICE_WIDTH - 83 - 6 * 4) / 5;
        const cusLeft = 9 + index * (4 + marginX);
        const cusBackColor = temp === item ? 'transparent' : '#fff';
        return (
          <View
            key={item}
            style={[
              styles.dotView,
              {left: cusLeft, backgroundColor: cusBackColor},
            ]}
          />
        );
      })}
    </View>
  );
};

export const styles = StyleSheet.create({
  wrapper: {
    position: 'relative',
    flexDirection: 'row',
    height: 60,
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
  },
  min: {
    position: 'absolute',
    left: 0,
    top: 17.5,
    fontSize: 12,
    color: '#000',
  },
  max: {
    position: 'absolute',
    right: 0,
    top: 17.5,
    fontSize: 12,
    color: '#000',
  },
  dotView: {
    width: 4,
    height: 4,
    borderRadius: 2,
    position: 'absolute',
    bottom: 14,
    backgroundColor: 'red',
  },
  track: {
    height: 14,
    borderRadius: 7,
    backgroundColor: '#0000ff',
  },
  thumb: {
    height: 20,
    width: 20,
    backgroundColor: '#77BC1F',
    borderColor: '#fff',
    borderWidth: 2,
    shadowRadius: 10,
    shadowOpacity: 0.1,
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
  },
  extra: {
    marginLeft: 20,
    width: 50,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  text: {
    color: '#77BC1F',
    fontSize: 17,
    marginRight: 5,
  },
  outer: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    bottom: 2,
  },
  inner: {
    width: 15,
    height: 15,
    backgroundColor: 'blue',
    borderRadius: 7.5,
  },
  val: {
    position: 'absolute',
    backgroundColor: '#77BC1F',
    height: 25,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 12.5,
    top: -32,
    width: 60,
    left: -20,
  },
  sliderStyle: {flex: 1, height: 33},
  thumbStyle: {height: 20, width: 20, backgroundColor: 'transparent'},
  textStyle: {color: '#fff', fontSize: 16, fontWeight: 'bold'},
});
