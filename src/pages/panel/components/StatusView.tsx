/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2023-02-09 11:35:30
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-07-17 15:19:29
 * @FilePath: /61004/src/pages/panel/components/StatusView
 * @Description: 设备状态入口卡片封装
 */
import React, {useEffect} from 'react';
import {StyleSheet, View, Text, ViewStyle} from 'react-native';
import {CVNIcon} from 'cvn-panel-kit';
import {observer} from 'mobx-react';
import Strings from '@i18n';
import DeviceConnectionStore from 'IOTRN/src/mobx/deviceConnectionStore';
/**
 * 设备状态入口卡片
 * @param {Object} store - 注入的store对象
 * @param {Object} style - 自定义样式
 * @param {boolean} disabled - 是否禁用
 * @param {Object} home - 主页数据
 * @returns {Node} - StatusView组件
 */
export const StatusView = observer(
  ({
    style = {},
    deviceConnectionStore,
  }: {
    style?: ViewStyle;
    deviceConnectionStore: DeviceConnectionStore;
  }) => {
    const {
      status, // mowing\charging
      statusTitle,
      remainTitlePrefix,
      showStatus = false,
    } = deviceConnectionStore.state;
    const {bladeRotationSpeed} =
      deviceConnectionStore.actions.getBladeRotationSpeed();
    const {minute, hour} = deviceConnectionStore.actions.getCurrentTimeObj;
    const disabled = deviceConnectionStore.actions.allDisabled;

    useEffect(() => {
      deviceConnectionStore.actions.getStatusInfo();
      deviceConnectionStore.actions.getRemainTitlePrefix();
    }, [
      deviceConnectionStore.actions,
      disabled,
      deviceConnectionStore.state.status,
      deviceConnectionStore.state.resWithNew,
    ]);

    if (disabled) {
      return null;
    }
    if (!showStatus) {
      return null;
    }
    const hourUnit = Strings.getLang('rn_61004_panelhome_hour_textview_text');
    const minuteUnit = Strings.getLang('rn_common_unit_min_textview_text');
    const speedUnit = Strings.getLang(
      'rn_61004_panelhome_bladspeed_unit_textview_text',
    );
    let iconSource = require('@assets/battery/61004_icon_status_mowing.png');
    if (status === 'charging') {
      iconSource = require('@assets/battery/61004_icon_status_charging.png');
    }
    return (
      <View style={[styles.container, style]}>
        <View style={[styles.topView]}>
          <CVNIcon size={35} source={iconSource} />
          <Text style={styles.title}>{statusTitle}</Text>
        </View>

        <View style={styles.subView}>
          <Text style={styles.subTitle}>{remainTitlePrefix}</Text>
          {status === 'charging' ? (
            <>
              {hour === '00' ? null : (
                <Text style={styles.valueText}>
                  {hour}
                  <Text style={styles.unitText}>{hourUnit}</Text>
                </Text>
              )}
              <Text style={styles.valueText}>
                {minute}
                <Text style={styles.unitText}>{minuteUnit}</Text>
              </Text>
            </>
          ) : (
            <Text style={styles.valueText}>
              {bladeRotationSpeed}
              <Text style={styles.unitText}>{speedUnit}</Text>
            </Text>
          )}
        </View>
      </View>
    );
  },
);
const styles = StyleSheet.create({
  container: {
    marginTop: 15,
    marginHorizontal: 14,
    justifyContent: 'center',
    backgroundColor: '#77BC1F1A',
    borderRadius: 10,
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowRadius: 15,
    shadowOpacity: 0.05,
    paddingBottom: 12,
  },
  topView: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  leftBox: {
    justifyContent: 'center',
    alignItems: 'center',
    borderTopLeftRadius: 10,
    borderBottomEndRadius: 10,
    backgroundColor: '#77BC1F',
    width: 35,
    height: 35,
  },
  title: {
    fontWeight: '500',
    color: '#77BC1F',
    fontSize: 22,
    marginLeft: 12,
  },
  subView: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    marginTop: 8,
  },
  subTitle: {
    marginLeft: 47,
    color: '#3C3936',
    fontSize: 14,
    fontWeight: '400',
  },
  valueText: {
    marginLeft: 5,
    color: '#000000',
    fontSize: 19,
    fontWeight: '500',
  },
  unitText: {
    marginLeft: 5,
    color: '#000000',
    fontSize: 14,
    fontWeight: '400',
  },
});
