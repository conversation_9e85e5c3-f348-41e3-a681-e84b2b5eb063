/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2023-02-09 11:35:30
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-07-17 14:59:57
 * @FilePath: /61004/src/pages/panel/components/ErrorView
 * @Description: 升级、设备告警view封装
 */

import React from 'react';
import {
  StyleSheet,
  View,
  TouchableWithoutFeedback,
  Text,
  ImageSourcePropType,
} from 'react-native';
import {CVNIcon} from 'cvn-panel-kit';
interface ErrorViewProps {
  text?: string;
  isShow?: boolean;
  style?: object;
  onPress?: () => void;
  source?: ImageSourcePropType;
}

/**
 * 错误视图组件
 */
export const ErrorView: React.FC<ErrorViewProps> = ({
  text = '',
  isShow = true,
  style = {},
  onPress = () => {},
  source = require('@assets/common/61004_common_icon_warning_red.png'),
}) => {
  if (!isShow) {
    return null;
  }
  return (
    <View style={[styles.errorView, style]}>
      <TouchableWithoutFeedback
        onPress={onPress}
        hitSlop={{top: 10, right: 10, left: 10, bottom: 10}}>
        <View style={styles.errorInView}>
          <View style={styles.leftBox}>
            <CVNIcon source={source} size={22} />
            <Text numberOfLines={1} style={styles.errorText}>
              {text}
            </Text>
          </View>
          <CVNIcon
            source={require('@assets/common/61004_common_icon_arrow_right.png')}
            size={20}
          />
        </View>
      </TouchableWithoutFeedback>
    </View>
  );
};
// PropTypes removed - using TypeScript interfaces instead
const styles = StyleSheet.create({
  errorView: {
    height: 40,
    paddingHorizontal: 10,
    backgroundColor: '#ffffff',
    borderRadius: 8,
    // opacity: 0.65,
    justifyContent: 'center',
  },
  errorInView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  leftBox: {
    flexDirection: 'row',
    alignItems: 'center',
    flexShrink: 2,
  },
  errorText: {
    marginLeft: 10,
    fontSize: 15,
    color: '#000000',
    flexShrink: 2,
    fontWeight: '500',
  },
});
