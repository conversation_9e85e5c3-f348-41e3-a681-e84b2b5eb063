/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2023-09-27 10:14:59
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-07-17 14:59:07
 * @FilePath: /61004/src/pages/panel/components/Disabled
 * @Description: Disabled View
 */
import React, {useEffect, useState} from 'react';
import {observer} from 'mobx-react';
import {StyleSheet} from 'react-native';
import {Switch} from 'react-native-elements';
import ModelMap from '@utils/model';
import {tracer, eleIdMap} from '@tracer';
import DeviceConnectionStore from 'IOTRN/src/mobx/deviceConnectionStore';
import DeviceConfigurationStore from 'IOTRN/src/mobx/deviceConfigurationStore';

/**
 * 禁用状态组件
 * @param {Object} store - 注入的store对象
 * @param {Object} style - 自定义样式
 * @param {boolean} connectDisabled - 连接禁用状态
 * @param {boolean} disabled - 禁用状态
 * @returns {Node} - Disabled组件
 */

interface InjectStore {
  deviceConnectionStore: DeviceConnectionStore;
  deviceConfigurationStore: DeviceConfigurationStore;
}
export const BackUpAlertSwitch = observer(
  ({deviceConnectionStore, deviceConfigurationStore}: InjectStore) => {
    const backUpAlertModeValue =
      deviceConfigurationStore.actions.backUpAlertModeValue;
    const [backAlertSwitch, setBackAlertSwitch] =
      useState(backUpAlertModeValue);
    useEffect(() => {
      setBackAlertSwitch(backUpAlertModeValue);
    }, [backUpAlertModeValue]);

    /**
     * @description: 切换报警灯模式
     * @param {Number} status
     */
    const switchAlarmMode = (status: boolean) => {
      setBackAlertSwitch(status);
      tracer.click({
        eleid: eleIdMap.Back_Up_Alert_Switch_Click,
        expand: {mode: `${Number(status)}`},
      });
      const params = {
        [ModelMap.back_up_alert_mode]: Number(status),
      };
      deviceConnectionStore.actions.editProperty({
        propertyData: params,
      });
    };

    return (
      <Switch
        style={styles.switchStyle}
        color="#77BC1F"
        onValueChange={switchAlarmMode}
        value={backAlertSwitch}
      />
    );
  },
);

const styles = StyleSheet.create({
  switchStyle: {
    position: 'absolute',
    right: 0,
  },
});
