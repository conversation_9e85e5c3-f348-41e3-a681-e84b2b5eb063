/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2023-02-09 11:35:35
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2023-05-25 18:29:49
 * @FilePath: /Tractor/src/pages/panel/components/Card
 * @Description: 首页card封装
 *
 */
import {StyleSheet, Text, View, ViewStyle} from 'react-native';
import React, {memo} from 'react';
import {CVNIcon} from 'cvn-panel-kit';

interface Props {
  children?: React.ReactNode;
  title: string;
  subTitle?: string;
  icon: string;
  titleStyle?: ViewStyle;
  cStyle?: ViewStyle;
  disabled?: boolean;
  rightDom?: React.ReactNode;
}

const Card = ({
  children,
  title,
  icon,
  subTitle,
  titleStyle = {},
  cStyle = {},
  disabled,
  rightDom,
}: Props) => {
  return (
    <View style={[styles.container, cStyle]}>
      <View style={styles.header}>
        <CVNIcon size={34} style={styles.icon} source={icon} />
        <Text style={[styles.title, titleStyle]}>{title}</Text>
        {subTitle ? (
          <View style={styles.subTitleBox}>
            <Text style={styles.sub} />
            <View style={styles.shortLine} />
            <Text style={styles.subTitle}>{subTitle}</Text>
          </View>
        ) : null}
        {rightDom}
      </View>
      {children ?? null}
      {disabled ? <View style={styles.disabled} /> : null}
    </View>
  );
};
export default memo(Card);
const styles = StyleSheet.create({
  container: {
    paddingTop: 13.5,
    paddingBottom: 15,
    paddingHorizontal: 15,
    backgroundColor: '#fff',
    borderRadius: 5,
    marginHorizontal: 15,
    marginBottom: 15,
    position: 'relative',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    // backgroundColor: 'blue',
  },
  icon: {},
  title: {
    marginLeft: 10.5,
    color: '#000000',
    fontSize: 17,
    fontWeight: '400',
  },
  subTitleBox: {
    flexDirection: 'row',
    alignItems: 'center',
    flexShrink: 2,
  },
  sp: {
    marginHorizontal: 30,
    color: '#000000',
    opacity: 0.5,
    fontSize: 15,
  },
  shortLine: {
    marginHorizontal: 12,
    backgroundColor: '#000000',
    opacity: 0.5,
    width: 0.5,
    height: 15,
  },
  sub: {
    color: '#000000',
    opacity: 0.7,
    fontSize: 12,
  },
  subTitle: {
    color: '#00000066',
    fontSize: 14,
    lineHeight: 16.7,
    flexShrink: 2,
  },
  disabled: {
    position: 'absolute',
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
    opacity: 0.4,
    backgroundColor: '#ffffff',
  },
});
