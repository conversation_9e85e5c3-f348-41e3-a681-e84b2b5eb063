import React from 'react';
import {View, StyleSheet, Text, ViewStyle} from 'react-native';
import {CVNIcon} from 'cvn-panel-kit';
interface IndicatorViewProps {
  style?: ViewStyle;
  source?: string;
  name?: string;
  value?: string;
}
export const IndicatorView = ({
  style = {},
  source = require('@assets/run/61004_run_speed.png'),
  name = '',
  value = '',
}: IndicatorViewProps) => {
  return (
    <View style={[styles.container, style]}>
      <CVNIcon source={source} size={20} />
      <View style={styles.textView}>
        <Text style={styles.nameText}>{name ?? ''}</Text>
        <Text style={styles.valueText}>{value ?? ''}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
  },
  textView: {
    marginLeft: 5,
    marginTop: 1.5,
  },
  nameText: {
    fontSize: 14,
    color: '#666666',
  },
  valueText: {
    marginTop: 3.5,
    fontSize: 16,
    color: '#000000',
  },
});
