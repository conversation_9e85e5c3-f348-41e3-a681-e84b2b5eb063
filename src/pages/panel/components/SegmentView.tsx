import React, {useEffect, useState} from 'react';
import {
  View,
  StyleSheet,
  Text,
  TouchableWithoutFeedback,
  DimensionValue,
} from 'react-native';
export const SegmentView = ({
  value = 0,
  onItemSelect = () => {},
  data,
}: {
  value: number;
  onItemSelect: (item: {title: string; mode: boolean}, index: number) => void;
  data: {title: string; mode: boolean}[];
}) => {
  const [selIndex, setSelIndex] = useState(value || 0);
  const [width, setWidth] = useState<DimensionValue>('auto');

  useEffect(() => {
    setSelIndex(value);
  }, [value]);
  useEffect(() => {
    if (data.length > 0) {
      const newWidth = 100 / data.length + '%';
      setWidth(newWidth as DimensionValue);
    }
  }, [data?.length]);

  return (
    <View style={styles.container}>
      {data.map((item, index) => {
        const selected = selIndex === index;
        return (
          <TouchableWithoutFeedback
            key={item.title}
            onPress={() => {
              setSelIndex(index);
              onItemSelect(item, index);
            }}>
            <View
              style={[
                styles.buttonView,
                {width: width},
                selected ? styles.activeView : styles.inActiveView,
              ]}>
              <Text
                style={[
                  styles.titleText,
                  selected ? styles.activeText : styles.inActiveText,
                ]}>
                {item.title}
              </Text>
            </View>
          </TouchableWithoutFeedback>
        );
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 4,
    marginTop: 24,
    backgroundColor: '#EDEEEF',
    borderRadius: 25,
    flexDirection: 'row',
    alignItems: 'center',
  },
  buttonView: {
    borderRadius: 21,
    height: 42,
    width: '33%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  activeView: {
    backgroundColor: '#77BC1F',
  },
  inActiveView: {
    backgroundColor: 'transparent',
  },
  titleText: {
    fontSize: 14,
  },
  activeText: {
    color: '#FFF',
    fontWeight: '500',
  },
  inActiveText: {
    color: '#00000099',
    fontWeight: '400',
  },
});
