/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2023-10-12 14:04:33
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-07-17 18:25:29
 * @FilePath: /61004/src/pages/panel/components/index
 * @Description: 组件封装
 */
import {ErrorView} from './ErrorView';
import HomeUsageCard from './HomeUsageCard';
import NotifyView from './NotifyView';
import {StatusView} from './StatusView';
import Dashboard from './Dashboard';
import {IndicatorView} from './IndicatorView';
import {KeyButton} from './KeyButton';
import RuncarCard from './RuncarCard';
import {SegmentView} from './SegmentView';
import {NewSlider} from './NewSlider';
import {BackUpAlertSwitch} from './BackUpAlertSwitch';

export {
  ErrorView,
  HomeUsageCard,
  NotifyView,
  StatusView,
  Dashboard,
  IndicatorView,
  KeyButton,
  RuncarCard,
  SegmentView,
  NewSlider,
  BackUpAlertSwitch,
};
