import {DEVICE_WIDTH} from 'IOTRN/src/utils/device';
import {StyleSheet} from 'react-native';

const styles = StyleSheet.create({
  topBackView: {
    position: 'absolute',
    width: DEVICE_WIDTH,
    height: DEVICE_WIDTH, // 临时
  },
  headerView: {
    backgroundColor: 'transparent',
    borderBottomWidth: 0,
  },
  deviceBackIcon: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  deviceImage: {
    width: 375,
    height: 275,
  },
  marginCard: {
    marginLeft: 14,
    marginRight: 14,
    marginTop: 13,
    marginBottom: 13,
  },
  marginCardDeffMargin: {},
  lightDelayBox: {
    marginHorizontal: 14,
  },
  switchStyle: {
    position: 'absolute',
    right: 0,
  },
  mowingDescription: {
    margin: 4,
    marginTop: 12,
    color: '#999999',
  },
  runCardHeight: {
    alignItems: 'center',
    height: 100,
  },
  headerBox: {
    alignItems: 'center',
    width: DEVICE_WIDTH - 100,
  },
  title: {
    color: '#020202',
    fontSize: 19,
    fontWeight: '600',
  },
  connectedView: {
    position: 'absolute',
    right: 25,
    top: 23,
  },
  iconBle: {
    marginTop: 10,
  },
  status: {
    color: '#000000',
    fontSize: 14,
    fontWeight: '400',
    marginTop: 4,
    opacity: 0.8,
  },
  rightBack: {
    width: 40,
    height: 40,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    marginRight: -20,
    paddingRight: 20,
  },
  deviceName: {
    fontSize: 14,
    color: '#000000',
    fontWeight: 'bold',
  },
  contentView: {
    marginTop: 20,
  },
  notifiyView: {
    marginHorizontal: 14,
  },
  boxView: {
    marginTop: 15,
    paddingHorizontal: 14,
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'space-between',
  },
  // 卡片，单独抽出来
  rightBox: {
    justifyContent: 'space-between',
    width: (DEVICE_WIDTH - 28 - 14) / 2,
  },
  cardTitle: {
    fontWeight: '500',
    fontSize: 17,
    marginLeft: 10,
  },
  dashboardView: {
    paddingTop: 13.5,
  },
});
export default styles;
