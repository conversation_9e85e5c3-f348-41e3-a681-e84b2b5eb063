/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2023-09-25 17:44:24
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-08-28 14:29:55
 * @FilePath: /61004/src/pages/panel/Home
 * @Description: 面板首页
 */
import React, {memo, useEffect, useRef} from 'react';
import {
  device,
  CommonEmitter,
  Utils,
  PageView,
  CVNIcon,
  WhiteSpace,
} from 'cvn-panel-kit';
import {
  Platform,
  Text,
  View,
  ScrollView,
  TouchableWithoutFeedback,
} from 'react-native';
import {inject, observer} from 'mobx-react';
import LinearGradient from 'react-native-linear-gradient';
import Strings from '@i18n';
import topicMap from 'IOTRN/src/utils/topic';
import {tracer, eleIdMap, pageIdMap} from '@tracer';
import {HeaderView} from '@components';
import {isIphoneX} from '@utils/device';
import {
  NotifyView,
  HomeUsageCard,
  StatusView,
  Dashboard,
  SegmentView,
  NewSlider,
  BackUpAlertSwitch,
} from './components';
import {checkPermission} from 'IOTRN/src/mobx/utils/help';
import RuncarCard from './components/RuncarCard';
import CardList from './components/CardList';
import Card from './components/Card';
import styles from './homeStyles';
import DeviceWorkModel from './utils/deviceWorkModel';
import {androidGoBack, goPage} from 'IOTRN/src/utils/pageAction';
import BleConnectionManager from './utils/bleConnectionManager';
import MessageProcess from './utils/messageProcess';
import PageBack from './utils/pageBack';
import {InjectStore} from 'IOTRN/src/types';
import {useFocusEffect} from '@react-navigation/native';

const {LogUtils} = Utils;

const STEPNUMCONFIG: {[key: string]: {index: number; value: number}} = {
  0: {
    index: 0,
    value: 0,
  },
  60: {
    index: 1,
    value: 5,
  },
  120: {
    index: 2,
    value: 15,
  },
  180: {
    index: 3,
    value: 30,
  },
  240: {
    index: 4,
    value: 100,
  },
  300: {
    index: 5,
    value: 300,
  },
};

const Home = inject('rootStore')(
  observer(({rootStore, ...props}: InjectStore) => {
    // 定义蓝牙连接管理器、设备工作模式、消息处理等引用
    const bleConnectionManagerRef = useRef<BleConnectionManager | null>(null);
    const deviceWorkModeRef = useRef<DeviceWorkModel | null>(null);
    const messageProcessRef = useRef<MessageProcess | null>(null);
    const deviceWorkMode = new DeviceWorkModel(rootStore.deviceConnectionStore); // 设备工作模式
    const pageBackRef = useRef<PageBack | null>(null);

    const fromOtaDone = useRef(false); // 是否从ota完成页面返回

    const androidGoBackTimerRef = useRef<number | null | NodeJS.Timeout>(null);
    const reCheckOtaTimerRef = useRef<number | null | NodeJS.Timeout>(null);

    useEffect(() => {
      LogUtils.init();
      bleConnectionManagerRef.current = new BleConnectionManager(rootStore); // 蓝牙通信
      deviceWorkModeRef.current = new DeviceWorkModel(
        rootStore.deviceConnectionStore,
      ); // 设备工作模式
      messageProcessRef.current = new MessageProcess(rootStore); // 消息处理
      pageBackRef.current = new PageBack(rootStore); // 页面返回

      // 检测位置权限
      checkPermission();

      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    // 监听页面失去焦点（相当于页面消失）
    useFocusEffect(
      React.useCallback(() => {
        // 页面获得焦点时的逻辑（可选）
        return () => {
          // 页面失去焦点时的逻辑（导航离开、切换标签页等）
          tracer.leave({pageid: pageIdMap.panelHome.toString()});
        };
      }, []),
    );

    useEffect(() => {
      // 页面加载完成，曝光上报埋点
      tracer.page();
      // RN push pop处理
      dealWithRNContainerPushOrPop();
      // topic数据处理
      bleConnectionManagerRef.current?.dealWithTopic();
      // 蓝牙数据处理
      bleConnectionManagerRef.current?.dealWithBleListener(); // 蓝牙数据处理
      // 蓝牙指令数据处理
      bleConnectionManagerRef.current?.dealWithBleCmdListener();
      // 蓝牙连接
      bleConnectionManagerRef.current?.dealWithBleConnect();
      // 订阅WIFI连接状态变化
      rootStore.deviceStore.actions.subscribeWithWIFI();
      // 处理设备消息,等原生加完后联调
      messageProcessRef.current?.dealWithMsg();
      // 处理安卓物理返回键监听
      dealWithAndroidBack();
      // 后台切到前台监听处理
      pageBackRef.current?.dealWithAppDidComeForeground();
      // 监听原生ota流程 点击done
      dealWithOtaDoneListener();
      // 处理推送消息弹框逻辑
      messageProcessRef.current?.dealWithPushNotification();
      // 配件接口
      messageProcessRef.current?.dealWithPartsDetail();
      // 设备详情
      getDetailList();
      // 数据统计二氧化碳总排放量
      messageProcessRef.current?.getTotalCo2Reduced();
      // 获取故障消息列表,并设置数据源
      messageProcessRef.current?.requestHomeMsgList();
      // status小红点判断
      messageProcessRef.current?.requestErrorStatusList();

      messageProcessRef.current?.getUsageHistory();
      // 处理返回该页面时，重新的更新折线图数据
      const unsubscribe = props?.navigation.addListener('focus', () => {
        if (rootStore.deviceStore.state.initialParams.deviceId) {
          messageProcessRef.current?.getUsageHistory();
        }
      });
      return () => {
        // 页面消失时，上报页面停留时间埋点信息
        tracer.leave({pageid: pageIdMap.panelHome.toString()});
        // 页面消失时，移除监听器，防止内存泄露
        unsubscribe?.remove?.();
        dealWithWillUnmount();
        rootStore.deviceConnectionStore.actions.resetData();
      };
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [rootStore.deviceStore.state.initialParams.deviceId]);

    /**
     * 获取设备详情
     */
    const getDetailList = () => {
      rootStore.requestDeviceDetail(
        {
          req: rootStore.deviceStore.state.initialParams.deviceId,
        },
        // ota升级检测，一次调用，且在线
        () => bleConnectionManagerRef.current?.dealWithOtaUpdate(),
      );
    };

    /**
     * @description: 原生按钮点击 done之后，重新检查是否还有ota升级
     */
    const dealWithOtaDoneListener = () => {
      // Remove previous listener to prevent memory leaks
      CommonEmitter.removeAllListeners('otaUpdateDidCompleted');

      CommonEmitter.addListener('otaUpdateDidCompleted', () => {
        fromOtaDone.current = true;
        // 本地置为false，防止上面 dealWithUpdate callBack不生效
        if (reCheckOtaTimerRef.current) {
          clearTimeout(reCheckOtaTimerRef.current);
        }
        reCheckOtaTimerRef.current = setTimeout(() => {
          rootStore.deviceConfigurationStore.actions.setShowRed(false);
          rootStore.deviceConfigurationStore.actions.setIsForceUpdate(false);
        }, 1000);
      });
    };
    /**
     * @description 定时器处理
     */
    const cleanTimers = () => {
      if (androidGoBackTimerRef.current) {
        clearTimeout(androidGoBackTimerRef.current);
        androidGoBackTimerRef.current = null;
      }
      if (reCheckOtaTimerRef.current) {
        clearTimeout(reCheckOtaTimerRef.current);
        reCheckOtaTimerRef.current = null;
      }
    };
    /**
     * 移除各种监听，防止内存泄露
     */
    const removeListenners = () => {
      CommonEmitter.removeAllListeners('bluetoothChange');
      CommonEmitter.removeAllListeners('deviceBleConnectStateChange');
      CommonEmitter.removeAllListeners('localDeviceDataChange');
      CommonEmitter.removeAllListeners('topicDataDidChange');
      CommonEmitter.removeAllListeners('otherCmdResponse');

      // ota listener
      CommonEmitter.removeAllListeners('cmdNotify');
      CommonEmitter.removeAllListeners('otaUpdateDidCompleted');
      CommonEmitter.removeAllListeners('otaUpdateDidReceived');

      // 消息 listener
      CommonEmitter.removeAllListeners('messageNotify');
      // 进入RN容器
      CommonEmitter.removeAllListeners('RNContainerViewWillAppear');
      // 离开RN容器
      CommonEmitter.removeAllListeners('RNContainerViewWillDisAppear');
      // android物理返回键监听
      if (Platform.OS === 'android') {
        CommonEmitter.removeAllListeners('keyBackDown');
      }
      // push notification
      CommonEmitter.removeAllListeners('didRecievePushNotification');
      pageBackRef.current?.destroy(); // app前后台切换监听销毁
    };
    /**
     * @description: 点击返回到原生vc时，监听、定时器等销毁
     */
    const dealWithWillUnmount = () => {
      cleanTimers();
      bleConnectionManagerRef.current?.clearTimer(); // 清除蓝牙定时器
      removeListenners();
      if (device.unsubscribe) {
        deviceUnSubscribe();
      }
    };
    /**
     * @description: 设备取消订阅处理
     */
    const deviceUnSubscribe = () => {
      const {deviceId} = rootStore.deviceStore.state.initialParams;
      const connectWIFITopic = `${topicMap.connectWIFI}${deviceId}`;
      const disConnectWIFITopic = `${topicMap.disConnectWIFI}${deviceId}`;
      const otaAcceptedTopic = `${topicMap.preFixWithoutSymbol}${deviceId}${topicMap.otaAcceptedSuffix}`;
      const shadowUpdateAcceptedStr = `${topicMap.preFix}${deviceId}${topicMap.shadowUpdateAcceptedSuffix}`;
      const shadowGetAcceptedStr = `${topicMap.preFix}${deviceId}${topicMap.shadowGetAcceptedSuffix}`;
      // WIFI连接
      device.unsubscribe(connectWIFITopic);
      // WIFI断开
      device.unsubscribe(disConnectWIFITopic);
      // 订阅物模型 接收
      device.unsubscribe(shadowUpdateAcceptedStr);
      // 物模型订阅，单次拉取
      device.unsubscribe(shadowGetAcceptedStr);
      // ota accepted
      device.unsubscribe(otaAcceptedTopic);
    };
    /**
     * @description: 监听 原生->RN， RN->原生
     */
    const dealWithRNContainerPushOrPop = () => {
      CommonEmitter.addListener('RNContainerViewWillAppear', () => {
        rootStore.deviceConnectionStore.actions.setIsInRNContainerVC(true);
        // 每当回到RN时，检查下是否还有强制升级，如有，弹框
        if (!fromOtaDone.current) {
          bleConnectionManagerRef.current?.handleAlertAndDot();
        }
        getDetailList();
        messageProcessRef.current?.dealWithPartsDetail();
        // 发送指令获取设备wifi信息和蓝牙信息方法
        rootStore.deviceConnectionStore.actions.getDeviceWifiInfo();
      });
      CommonEmitter.addListener('RNContainerViewWillDisAppear', () => {
        rootStore.deviceConnectionStore.actions.setIsInRNContainerVC(false);
        // 跳出RN vc时，该值赋值为false
        fromOtaDone.current = false;
        // 首页跳出去和设备详情页跳出去，内部可以区分pageid
      });
    };
    /**
     * @description: 处理安卓物理返回键
     */
    const dealWithAndroidBack = () => {
      if (Platform.OS === 'android') {
        CommonEmitter.addListener('keyBackDown', () => {
          // 安卓先不加，先解决iOS，生命周期不走问题
          if (LogUtils.currentPage === 'ViewPanelHome') {
            dealWithWillUnmount();
          }
          // 物理按键的时候处理
          rootStore.deviceConnectionStore.actions.setIsNativeGoback(true);
          if (LogUtils.currentPage === 'ViewDashboard') {
            androidGoBackTimerRef.current = setTimeout(() => {
              androidGoBack(props.navigation);
            }, 200);
          } else {
            androidGoBack(props.navigation);
          }
        });
      }
    };

    /**
     * @description: 头部设备名称
     */
    const onHeaderTitlePress = () => {
      pageBackRef.current?.onHeaderTitlePress(res => {
        goPage(props.navigation, 'ViewEditName', {
          defaultName: res,
          callBack: () => {
            getDetailList();
          },
        });
      });
    };

    const goPageDetail = (page: string, params?: object) => {
      goPage(props.navigation, page, params);
    };
    /**
     * @description: 导航栏右侧更多按钮
     */
    const onRightPress = () => {
      tracer.click({
        eleid: eleIdMap.Device_Details_Button_Click,
      });
      goPage(props.navigation, 'ViewDetailList');
    };
    /**
     * @description: 导航栏左侧返回按钮
     */
    const onLeftPress = () => {
      bleConnectionManagerRef.current?.dealWithGoBack();
    };

    const {bleConnected, connected} = rootStore.deviceConnectionStore.state;
    const {nickName} = rootStore.deviceStore.actions;
    const {mowingPerformanceMode, lightOffDelayValue} =
      rootStore.deviceConfigurationStore.actions;
    const disabled = !connected && !bleConnected;
    const cBackIconStyle = {marginTop: -87};
    const imageIconSource = require('@assets/home/<USER>');

    return (
      <PageView>
        <HeaderView
          style={styles.headerView}
          title={
            <TouchableWithoutFeedback onPress={() => onHeaderTitlePress()}>
              <View style={styles.headerBox}>
                <Text numberOfLines={1} style={styles.title}>
                  {nickName}
                </Text>
              </View>
            </TouchableWithoutFeedback>
          }
          rightElement={
            <View style={styles.rightBack}>
              <CVNIcon
                source={require('@assets/home/<USER>')}
              />
            </View>
          }
          onRightPress={() => {
            onRightPress();
          }}
          onLeftPress={() => {
            onLeftPress();
          }}
        />
        <ScrollView showsVerticalScrollIndicator={false}>
          <LinearGradient
            start={{x: 0, y: 0}}
            end={{x: 0, y: 1}}
            colors={['#FFFFFF', '#f7f7f7']}
          />
          <View style={styles.dashboardView}>
            {/* 电量、电池信息、开锁、查看电池 */}
            <Dashboard
              disabled={disabled}
              goPage={goPageDetail}
              batteryManagementStore={rootStore.batteryManagementStore}
              deviceConnectionStore={rootStore.deviceConnectionStore}
              deviceConfigurationStore={rootStore.deviceConfigurationStore}
            />
          </View>
          {/* 小车展示图 */}
          <View style={[styles.deviceBackIcon, cBackIconStyle]}>
            <CVNIcon
              style={styles.deviceImage}
              source={imageIconSource}
              resizeMode="stretch"
            />
          </View>
          {/* 小车运行时间、运行速度 */}
          <View style={styles.marginCard}>
            <RuncarCard
              disabled={disabled}
              style={styles.runCardHeight}
              deviceConnectionStore={rootStore.deviceConnectionStore}
            />
          </View>
          {/* 设备消息 */}
          <NotifyView
            style={styles.notifiyView}
            deviceConnectionStore={rootStore.deviceConnectionStore}
            deviceAbnormalStore={rootStore.deviceAbnormalStore}
            deviceStore={rootStore.deviceStore}
          />
          {/* 蓝牙、wifi连接状态 */}
          <StatusView deviceConnectionStore={rootStore.deviceConnectionStore} />
          {/* 注册信息模块、设备信息模块、升级模块、设备附件模块 */}
          <View style={styles.marginCard}>
            <CardList disabled={disabled} rootStore={rootStore} />
          </View>
          {/* 设备刀盘模式设置 */}
          <View style={styles.marginCardDeffMargin}>
            <Card
              cStyle={styles.lightDelayBox}
              disabled={disabled}
              title={Strings.getLang(
                'rn_61004_panelhome_mowingperfomance_textview_text',
              )}
              titleStyle={styles.cardTitle}
              icon={require('@assets/mow/61004_mowing_performance.png')}>
              <SegmentView
                key="daytime_ligh"
                data={[
                  {
                    title: Strings.getLang(
                      'rn_61004_panelhome_mowingstardard_textview_text',
                    ),
                    mode: false,
                  },
                  {
                    title: Strings.getLang(
                      'rn_61004_panelhome_mowinghyper_textview_text',
                    ),
                    mode: true,
                  },
                ]}
                value={mowingPerformanceMode ? 1 : 0}
                onItemSelect={item => {
                  deviceWorkMode.switchPerformanceMode(item.mode);
                }}
              />
              <Text style={styles.mowingDescription}>
                {mowingPerformanceMode
                  ? Strings.getLang(
                      'rn_61004_panelhome_mowinghyper_description_textview_text',
                    )
                  : Strings.getLang(
                      'rn_61004_panelhome_mowingstandard_description_textview_text',
                    )}
              </Text>
            </Card>
          </View>
          {/* 倒车警报 */}
          <View style={styles.marginCardDeffMargin}>
            <Card
              cStyle={styles.lightDelayBox}
              disabled={disabled}
              title={Strings.getLang(
                'rn_61004_panelhome_backupalert_textview_text',
              )}
              titleStyle={styles.cardTitle}
              icon={require('@assets/home/<USER>')}
              rightDom={
                <BackUpAlertSwitch
                  deviceConnectionStore={rootStore.deviceConnectionStore}
                  deviceConfigurationStore={rootStore.deviceConfigurationStore}
                />
              }
            />
          </View>
          {/* 灯光延迟关闭 */}
          <View style={styles.marginCardDeffMargin}>
            <Card
              cStyle={styles.lightDelayBox}
              disabled={disabled}
              title={Strings.getLang(
                'rn_tractor_panelhome_lightdelay_textview_text',
              )}
              titleStyle={styles.cardTitle}
              subTitle={`${Strings.getLang(
                'rn_tractor_panelhome_willturnoff_textview_text',
              )} ${STEPNUMCONFIG[lightOffDelayValue]?.value}s`}
              icon={require('@assets/home/<USER>')}>
              <NewSlider
                value={lightOffDelayValue}
                onSlidingComplete={(v: {index: number; value: number}) => {
                  deviceWorkMode.switchLightOffDelay(v);
                  tracer.click({
                    eleid: eleIdMap.LightOff_Delay_Slide_Click,
                    expand: {delayofftime: `${Number(v.value)}`},
                  });
                }}
                maximumValue={300}
                minimumValue={0}
              />
            </Card>
          </View>
          {/* 设备使用情况、充电次数、充电时长、行驶距离、能耗统计、行驶时间、行驶里程、能耗统计、行驶时间、行驶里程 */}
          <View style={styles.marginCardDeffMargin}>
            <HomeUsageCard
              goPage={goPageDetail}
              deviceChartStore={rootStore.deviceChartStore}
              resWithNew={rootStore.deviceConnectionStore.state.resWithNew}
              homeChartData={rootStore.deviceChartStore.state.homeChartData}
            />
          </View>
          <WhiteSpace size={isIphoneX ? 34 : 0} />
        </ScrollView>
      </PageView>
    );
  }),
);
export default memo(Home);
