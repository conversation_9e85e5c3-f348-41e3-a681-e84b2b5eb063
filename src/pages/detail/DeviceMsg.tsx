import React, {useCallback, useEffect, useMemo} from 'react';
import {Text, View, StyleSheet, ScrollView} from 'react-native';
import {PageView} from 'cvn-panel-kit';
import {Item, HeaderView} from '@components';
import {observer} from 'mobx-react';
import Strings from '@i18n';
import pkg from '../../../package.json';
import {goBack} from 'IOTRN/src/utils/pageAction';
import tracer, {eleIdMap, pageIdMap} from 'IOTRN/src/utils/tracer';
import {DeviceDetailProps} from 'IOTRN/src/types/device';

// 常量定义
const DEVICE_INFO_FIELDS = {
  MODEL_NO: 'MODEL NO.',
  SERIAL_NUMBER: 'Serial number',
  DEVICE_ID: 'Device ID',
  ASSEMBLY_SERIAL_NUMBER: 'Assembly serial number',
  FIRMWARE_VERSION: 'Firmware Version',
  RN_VERSION: 'RN Version',
} as const;

// 本地化键值常量
const LOCALIZATION_KEYS = {
  TITLE: 'rn_common_devicemsg_title_textview_text',
  MODEL_NO: 'rn_common_devicemsg_modelno_textview_text',
  SN: 'rn_common_devicemsg_sn_textview_text',
  DEVICE_ID: 'rn_common_devicemsg_deviceid_textview_text',
  ASSEMBLY_SN: 'rn_common_devicemsg_assemblysn_textview_text',
  VERSION: 'rn_common_devicemsg_version_textview_text',
  RN_VERSION: 'rn_common_devicemsg_rnversion_textview_text',
} as const;

// 类型定义
interface DeviceInfoItem {
  readonly title: string;
  readonly key: string;
  readonly content: string;
}

interface DeviceMsgRouteParams {
  readonly detail?: DeviceDetailProps;
  readonly [key: string]: unknown;
}

interface DeviceMsgProps {
  readonly route?: {
    readonly params?: DeviceMsgRouteParams;
  };
  readonly navigation?: {
    readonly goBack: () => void;
    readonly [key: string]: Function;
  };
}
// 设备信息配置数据
const createDeviceInfoTemplate = (
  detail: DeviceDetailProps,
): DeviceInfoItem[] => {
  const tmpDetail = {...detail} as {
    commodityModel?: string;
    sn?: string;
    deviceId?: string;
    assemblySnList?: string[];
    version?: string;
  };
  return [
    {
      title: Strings.getLang(LOCALIZATION_KEYS.MODEL_NO),
      key: DEVICE_INFO_FIELDS.MODEL_NO,
      content: detail?.commodityModel ?? '',
    },
    {
      title: Strings.getLang(LOCALIZATION_KEYS.SN),
      key: DEVICE_INFO_FIELDS.SERIAL_NUMBER,
      content: detail?.sn ?? '',
    },
    {
      title: Strings.getLang(LOCALIZATION_KEYS.DEVICE_ID),
      key: DEVICE_INFO_FIELDS.DEVICE_ID,
      content: detail?.productId ?? '', // 使用 productId 作为设备ID
    },
    {
      title: Strings.getLang(LOCALIZATION_KEYS.ASSEMBLY_SN),
      key: DEVICE_INFO_FIELDS.ASSEMBLY_SERIAL_NUMBER,
      content: tmpDetail?.assemblySnList?.join() ?? '', // DeviceDetailProps 中没有 assemblySnList，返回空字符串
    },
    {
      title: Strings.getLang(LOCALIZATION_KEYS.VERSION),
      key: DEVICE_INFO_FIELDS.FIRMWARE_VERSION,
      content: detail?.version ?? '',
    },
    {
      title: Strings.getLang(LOCALIZATION_KEYS.RN_VERSION),
      key: DEVICE_INFO_FIELDS.RN_VERSION,
      content: pkg?.version ?? '',
    },
  ];
};

const DeviceMsg = observer((props: DeviceMsgProps) => {
  // 使用 useMemo 优化数据计算
  const deviceDetail = useMemo(() => {
    return props.route?.params?.detail ?? ({} as DeviceDetailProps);
  }, [props.route?.params?.detail]);

  const deviceInfoData = useMemo(() => {
    return createDeviceInfoTemplate(deviceDetail);
  }, [deviceDetail]);

  // 页面追踪效果
  useEffect(() => {
    // 设备消息页面曝光事件上报
    tracer.page();
    return () => {
      // 设备信息页面停留时长上报
      tracer.leave({pageid: pageIdMap.deviceMsg.toString()});
    };
  }, []);

  // 返回按钮处理
  const handleLeftPress = useCallback(() => {
    if (props?.navigation) {
      // 返回按钮点击事件上报
      tracer.click({eleid: eleIdMap.Return_Button_Click});
      goBack(props.navigation);
    }
  }, [props.navigation]);

  return (
    <PageView>
      <HeaderView
        title={Strings.getLang(LOCALIZATION_KEYS.TITLE)}
        onLeftPress={handleLeftPress}
      />
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <Card data={deviceInfoData} />
      </ScrollView>
    </PageView>
  );
});

export default DeviceMsg;

// Card 组件优化
interface CardProps {
  readonly data: DeviceInfoItem[];
}

// 常量定义
const ASSEMBLY_SERIAL_INDEX = 3;

/**
 * 渲染普通项目
 */
const renderNormalItem = (
  item: DeviceInfoItem,
  index: number,
  dataLength: number,
) => (
  <Item
    iconShow={false}
    containerStyle={
      index === ASSEMBLY_SERIAL_INDEX ? styles.containerStyle : {}
    }
    itemStyle={index === ASSEMBLY_SERIAL_INDEX ? styles.containerStyle : {}}
    hideLine={index === dataLength - 1 || index === ASSEMBLY_SERIAL_INDEX}
    lineStyle={styles.lineStyle}
    key={item.title}
    title={item.title}
    titleStyle={styles.cusTitle}
    rightElement={<Text style={styles.rightText}>{item.content}</Text>}
  />
);

export const Card = React.memo<CardProps>(({data}) => {
  // 错误边界处理
  if (!data || data.length === 0) {
    return (
      <View style={styles.cardBox}>
        <Text style={styles.rightText}>
          {Strings.getLang('rn_common_no_data_text') || 'No data available'}
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.cardBox}>
      {data.map((item, index) => {
        return renderNormalItem(item, index, data.length);
      })}
    </View>
  );
});
// 样式常量
const COLORS = {
  WHITE: '#ffffff',
  BLACK: '#000000',
  GRAY_LIGHT: '#999999',
  GRAY_MEDIUM: '#333333',
  BORDER_GRAY: '#E5E5E5',
} as const;

const SPACING = {
  SMALL: 8,
  MEDIUM: 10,
  LARGE: 15,
  EXTRA_LARGE: 16,
  XXL: 20,
} as const;

const FONT_SIZES = {
  SMALL: 12,
  MEDIUM: 15,
} as const;

const styles = StyleSheet.create({
  cardBox: {
    backgroundColor: COLORS.WHITE,
  },
  rightText: {
    color: COLORS.GRAY_LIGHT,
    fontSize: FONT_SIZES.SMALL,
    flexShrink: 2,
    marginLeft: SPACING.SMALL,
    textAlign: 'right', // 右对齐文本
  },
  cusItemBox: {
    backgroundColor: COLORS.WHITE,
  },
  cusItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.EXTRA_LARGE,
    paddingHorizontal: SPACING.XXL,
    paddingRight: SPACING.EXTRA_LARGE,
    justifyContent: 'space-between',
    backgroundColor: COLORS.WHITE,
  },
  cusTitle: {
    color: COLORS.BLACK,
    fontSize: FONT_SIZES.MEDIUM,
  },
  cusContent: {
    color: COLORS.GRAY_LIGHT,
    fontSize: FONT_SIZES.SMALL,
    textAlign: 'right',
    flexShrink: 2,
    marginLeft: SPACING.MEDIUM,
  },
  cusLine: {
    backgroundColor: COLORS.BORDER_GRAY,
    height: 0.4,
    marginLeft: SPACING.XXL,
    marginRight: SPACING.EXTRA_LARGE,
  },
  lineStyle: {
    marginLeft: SPACING.XXL,
    marginRight: SPACING.EXTRA_LARGE,
  },
  scrollContainer: {
    paddingTop: SPACING.LARGE,
  },
  containerStyle: {
    height: 'auto',
  },
});
