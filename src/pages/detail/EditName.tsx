import React, {memo, useEffect, useState} from 'react';
import {Text, View, StyleSheet, ScrollView, TextInput} from 'react-native';
import {inject, observer} from 'mobx-react';
import Strings from '@i18n';
import {Button} from 'react-native-elements';
import {mobile, PageView} from 'cvn-panel-kit';
import {HeaderView} from '@components';
import {tracer, eleIdMap, pageIdMap} from '@tracer';
import {goBack} from 'IOTRN/src/utils/pageAction';
import {deviceEdit} from 'IOTRN/src/api';
import {RootStore} from 'IOTRN/src/mobx/rootStore';

interface EditNameRouteParams {
  defaultName?: string;
  callBack?: () => void;
}

interface EditNameRoute {
  params?: EditNameRouteParams;
}

const EditName = inject('rootStore')(
  observer(
    ({
      rootStore,
      route,
      navigation,
    }: {
      rootStore: RootStore;
      route: EditNameRoute;
      navigation: {[key: string]: Function};
    }) => {
      useEffect(() => {
        // 修改设备名称对话框页面曝光事件上报
        tracer.page();
        return () => {
          // 修改设备名称对话框页面停留时长事件上报
          tracer.leave({
            pageid: pageIdMap.editName.toString(),
          });
        };
      }, []);
      /**
       * Get string parameter from route params
       */
      const getStringParam = (params: keyof EditNameRouteParams): string => {
        const navigationParams = route?.params;
        const value = navigationParams?.[params];
        return typeof value === 'string' ? value : '';
      };

      /**
       * Get function parameter from route params
       */
      const getFunctionParam = (
        params: keyof EditNameRouteParams,
      ): (() => void) | undefined => {
        const navigationParams = route?.params;
        const value = navigationParams?.[params];
        return typeof value === 'function' ? value : undefined;
      };

      const defaultName = getStringParam('defaultName') ?? '';
      const [name, setName] = useState(defaultName);
      const [count, setCount] = useState(defaultName?.length ?? 0);

      const onSave = () => {
        tracer.click({
          eleid: eleIdMap.Edit_Name_Confirm_Button_Click,
        });

        if (!name || name.trim().length === 0) {
          mobile.toast(
            Strings.getLang(
              'rn_common_deviceeditname_notallowempty_button_text',
            ),
          );
          return;
        }

        const deviceId = rootStore.deviceStore.state.initialParams?.deviceId;
        if (!deviceId) {
          console.error('Device ID is missing');
          return;
        }

        deviceEdit({
          deviceId,
          deviceNickname: name.trim(),
        })
          .then(() => {
            mobile.toast(
              Strings.getLang('rn_common_operation_success_textview_text'),
              () => {},
            );
            const callBack = getFunctionParam('callBack');
            // 刷新列表
            if (callBack) {
              callBack();
            }
            goBack(navigation);
          })
          .catch((error: string) => {
            console.error('Error editing device name:', error);
            mobile.toast(
              Strings.getLang('rn_common_operation_failed_textview_text'),
              () => {},
            );
          });
      };
      return (
        <PageView>
          <HeaderView
            title={Strings.getLang(
              'rn_common_deviceeditname_title_textview_text',
            )}
            useCommonEleId={false}
            onLeftPress={() => {
              tracer.click({eleid: eleIdMap.Edit_Name_Cancel_Button_Click});
              goBack(navigation);
            }}
          />
          <ScrollView>
            <View style={styles.inputBox}>
              <TextInput
                maxLength={20}
                style={styles.itemText}
                placeholder={Strings.getLang(
                  'rn_common_deviceeditname_placeholder_input_text',
                )}
                defaultValue={name}
                value={name}
                onChangeText={v => {
                  const value = v;
                  if (value.length <= 20) {
                    setName(value);
                    setCount(value?.length ?? 0);
                  }
                }}
              />
              <Text style={styles.rightText}>{`${count}/20`}</Text>
            </View>
            <Text style={styles.descText}>
              {Strings.getLang('rn_common_deviceeditname_desc_textview_text')}
            </Text>
            <Button
              onPress={onSave}
              title={Strings.getLang(
                'rn_common_deviceeditname_save_button_text',
              )}
              containerStyle={styles.buttonContaner}
              buttonStyle={styles.button}
              titleStyle={styles.title}
            />
          </ScrollView>
        </PageView>
      );
    },
  ),
);

export default memo(EditName);
const styles = StyleSheet.create({
  inputBox: {
    marginTop: 10,
    height: 50,
    borderRadius: 5,
    marginHorizontal: 10,
    backgroundColor: '#ffffff',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15,
    justifyContent: 'space-between',
  },
  itemText: {
    fontSize: 16,
    color: '#000000',
    backgroundColor: '#ffffff',
    height: '100%',
    width: '85%',
  },
  rightText: {
    fontSize: 15,
    color: '#999999',
  },
  descText: {
    fontSize: 15,
    color: '#999999',
    marginHorizontal: 25,
    marginTop: 10,
  },
  buttonContaner: {
    marginHorizontal: 15,
    height: 48,
    marginTop: 30,
  },
  button: {
    backgroundColor: '#77BC1F',
    borderRadius: 2,
    height: 48,
  },
  title: {
    color: '#FFFFFF',
    fontSize: 18,
  },
});
