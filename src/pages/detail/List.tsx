import React, {memo, useCallback, useEffect, useMemo, useState} from 'react';
import {Text, View, ScrollView, StyleSheet} from 'react-native';
import {inject, observer} from 'mobx-react';
import {useFocusEffect} from '@react-navigation/native';
import {useModal} from '@components/Modal';
import {
  mobile,
  CommonEmitter,
  Utils,
  WhiteSpace,
  PageView,
} from 'cvn-panel-kit';
import {HeaderView, Item} from '@components';
import {Button} from 'react-native-elements';
import {DEVICE_WIDTH, isIphoneX} from '@utils/device';
import Strings from '@i18n';
import {tracer, eleIdMap, pageIdMap} from 'IOTRN/src/utils/tracer';
import {onHideDetailListAlert, onShowDetailListAlert} from './utils';
import {jumpToParts} from 'IOTRN/src/mobx/utils/help';
import {goBack, goPage} from 'IOTRN/src/utils/pageAction';
import {
  deviceResetPassword,
  deviceUnbind,
  postToFetchWhetherDeviceHasUnreadMessage,
} from 'IOTRN/src/api';
import {InjectStore} from 'IOTRN/src/types/common';

// 类型定义
type RegionType = 'NA' | 'EU';

interface ListItem {
  icon: string;
  title: string;
  key: string;
  regions: RegionType[]; // NA: 北美 EU: 欧洲
  rightExtra: React.ReactNode; // 右侧额外信息
  onItemClicked: () => void;
}

interface BaseCardProps {
  data: ListItem[];
  connected: boolean;
  bleConnected: boolean;
}

// 常量定义
const {JumpUtils} = Utils;

// 本地化键值常量
const LOCALIZATION_KEYS = {
  DEVICE_NAME: 'rn_common_detaillist_devicename_textview_text',
  DEVICE_STATUS: 'rn_61004_detaillist_devicestatus_textview_text',
  DEVICE_NOTIFICATION: 'rn_61004_detaillist_device_notification_textview_text',
  WIFI_CONFIGURE: 'rn_61004_detaillist_wificonfig_textview_text',
  REGISTRATION: 'rn_common_detaillist_registration_textview_text',
  UPGRADE: 'rn_common_detaillist_upgrade_textview_text',
  DATA_STATIC: 'rn_common_detaillist_statistics_textview_text',
  ABOUT: 'rn_common_detaillist_about_textview_text',
  CHANGE_PASSWORD: 'rn_61004_detaillist_changepassword_textview_text',
  PARTS: 'rn_common_detaillist_parts_textview_text',
  PRODUCT_INFO: 'rn_common_detaillist_productintro_textview_text',
  SHARE_DEVICE: 'rn_61004_detaillist_share_device_textview_text',
  FEEDBACK: 'rn_common_detaillist_feedback_textview_text',
} as const;

// 向后兼容的常量别名
const commonDeviceNameKey = LOCALIZATION_KEYS.DEVICE_NAME;
const commonDeviceNotification = LOCALIZATION_KEYS.DEVICE_NOTIFICATION;
const commonwificonfigureKey = LOCALIZATION_KEYS.WIFI_CONFIGURE;
const commonRegistratonKey = LOCALIZATION_KEYS.REGISTRATION;
const commonUpgradeKey = LOCALIZATION_KEYS.UPGRADE;
const commonDataStaticKey = LOCALIZATION_KEYS.DATA_STATIC;
const commonAboutKey = LOCALIZATION_KEYS.ABOUT;
const commonPartsKey = LOCALIZATION_KEYS.PARTS;
const commonProductInfoKey = LOCALIZATION_KEYS.PRODUCT_INFO;
const commonShareDeviceKey = LOCALIZATION_KEYS.SHARE_DEVICE;
const commonFeedBackKey = LOCALIZATION_KEYS.FEEDBACK;

// 性能优化常量
const REGIONS: Record<string, RegionType> = {
  NA: 'NA',
  EU: 'EU',
};

const SHARE_TYPE = {
  MAIN_ACCOUNT: 1,
} as const;

const DEVICE_RESET_IDENTIFIER = '24006';

const List = inject('rootStore')(
  observer(({rootStore, navigation}: InjectStore) => {
    const [hasUnreadMessage, setHasUnreadMessage] = useState<boolean>(false);
    const [baseData, setBaseData] = useState<ListItem[]>([]);

    const {deviceId, productId} = rootStore.deviceStore.state.initialParams;

    const onLeftPress = useCallback(() => {
      tracer.click({eleid: eleIdMap.Return_Button_Click});
      goBack(navigation);
    }, [navigation]);
    const {showRed = false} = rootStore.deviceConfigurationStore.state;
    const {deviceDetail: detail, wifiName} = rootStore.deviceStore.state;
    const {
      connected = false, // WIFI 连接状态
      bleConnected = false, // 蓝牙连接状态
    } = rootStore.deviceConnectionStore.state;

    const nickName = detail?.nickName ? detail?.nickName : detail?.deviceName;
    const {showModal} = useModal();
    /**
     * 获取设备详情
     */
    const getDetailList = useCallback(() => {
      rootStore.requestDeviceDetail({
        req: deviceId,
      });
    }, [rootStore, deviceId]);

    /**
     * 获取设备是否有未读消息
     */
    const getWhetherDeviceHasUnreadMessage = useCallback(() => {
      postToFetchWhetherDeviceHasUnreadMessage({
        deviceId: deviceId,
      })
        .then(({entry}: {entry: boolean}) => {
          setHasUnreadMessage(entry);
        })
        .catch((error: unknown) => {
          console.error('Failed to fetch unread message status:', error);
          // 设置默认值，避免UI异常
          setHasUnreadMessage(false);
        });
    }, [deviceId]);

    // 使用 useMemo 优化数据源计算
    const baseDataSource = useMemo(() => {
      const {canBeShared} = rootStore.deviceStore.actions;
      return [
        {
          icon: require('@assets/list/61004_list_name.png'),
          title: Strings.getLang(commonDeviceNameKey),
          key: commonDeviceNameKey,
          regions: [REGIONS.NA, REGIONS.EU],
          rightExtra: (
            <Text
              numberOfLines={1}
              style={[styles.rightExtraText, styles.deviceName]}>
              {detail?.nickName ? detail?.nickName : detail?.deviceName}
            </Text>
          ),
          onItemClicked: () => {
            tracer.click({
              eleid: eleIdMap.Equipment_Name_Button_Click,
            });
            const defaultName = detail?.nickName
              ? detail?.nickName
              : detail?.deviceName;
            goPage(navigation, 'ViewEditName', {
              defaultName,
              callBack: () => {
                getDetailList(); // 更新设备详情
              },
            });
          },
        },
        {
          icon: require('@assets/list/61004_list_wifi_configuration.png'),
          title: Strings.getLang(commonwificonfigureKey),
          key: commonwificonfigureKey,
          regions: [REGIONS.NA, REGIONS.EU],
          rightExtra: <Text style={styles.rightExtraText} />,
          onItemClicked: () => {
            if (!bleConnected) {
              showModal({
                content: Strings.getLang(
                  'rn_61004_wificonfig_bledisconnected_textview_text',
                ),
                onConfirm: () => {},
              });
              return;
            }
            const productObj = {
              cvn_product_id: productId,
            };
            tracer.click({
              eleid: eleIdMap.Product_Information_Button_Click,
            });
            const productJsonInformation = JSON.stringify(productObj);
            if (wifiName) {
              JumpUtils.jumpTo('ChervonIot://EGO/NetworkConfig/currentWifi', {
                sourceType: 'RN',
                deviceId,
                productJsonInformation,
                offline: connected,
                wifiName,
              });
            } else {
              JumpUtils.jumpTo('ChervonIot://EGO/NetworkConfig/selectWifi', {
                sourceType: 'RN',
                deviceId,
                productJsonInformation,
                offline: connected,
                wifiName,
              });
            }
          },
        },
        {
          icon: require('@assets/list/61004_list_device_notification.png'),
          title: Strings.getLang(commonDeviceNotification),
          key: commonDeviceNotification,
          regions: [REGIONS.NA, REGIONS.EU],
          rightExtra: hasUnreadMessage ? (
            <View style={styles.redDot} />
          ) : (
            <Text style={styles.rightExtraText} />
          ),
          onItemClicked: () => {
            tracer.click({
              eleid: eleIdMap.Device_Notification_Button_Click,
            });
            JumpUtils.jumpTo('ChervonIot://EGO/MessageCenter/MessageList', {
              sourceType: 'RN',
              deviceId,
              messageType: 2,
            });
          },
        },
        {
          icon: require('@assets/list/61004_list_icon_regis.png'),
          title: Strings.getLang(commonRegistratonKey),
          key: commonRegistratonKey,
          regions: canBeShared ? [REGIONS.NA, REGIONS.EU] : [],
          rightExtra:
            Number(detail?.infoStatus) === 1 ? (
              <Text style={[styles.registerText, styles.rightExtraText]}>
                {Strings.getLang(
                  'rn_common_detaillist_registered_textview_text',
                )}
              </Text>
            ) : (
              <Text style={[styles.registerText, styles.rightExtraText]}>
                {Strings.getLang(
                  'rn_common_detaillist_unregistered_textview_text',
                )}
              </Text>
            ),
          onItemClicked: () => {
            tracer.click({
              eleid: eleIdMap.Common_Device_Registration_Button_Click,
            });
            JumpUtils.jumpToRegistration(detail, deviceId);
          },
        },
        {
          icon: require('@assets/list/61004_list_icon_update_list.png'),
          title: Strings.getLang(commonUpgradeKey),
          key: commonUpgradeKey,
          regions: [REGIONS.NA, REGIONS.EU],
          rightExtra: showRed ? (
            <View style={styles.redDot} />
          ) : (
            <Text style={styles.rightExtraText} />
          ),
          onItemClicked: () => {
            tracer.click({
              eleid: eleIdMap.Common_Firmware_Update_Button_Click,
            });
            const url = `ChervonIot://EGO/DeviceManage/OTAInfo?deviceId=${deviceId}&singleMcu=${true}&upgradeModel=${0}&isSubset=${false}&deviceWifiIsOnline=${connected}`;
            mobile.jumpTo(url, () => {});
          },
        },
        {
          icon: require('@assets/list/61004_list_data_static.png'),
          title: Strings.getLang(commonDataStaticKey),
          key: commonDataStaticKey,
          regions: [REGIONS.NA, REGIONS.EU],
          rightExtra: <Text style={styles.rightExtraText} />,
          onItemClicked: () => {
            goPage(navigation, 'ViewStatistics');
          },
        },
        {
          icon: require('@assets/list/61004_list_icon_baike.png'),
          title: Strings.getLang(commonProductInfoKey),
          key: commonProductInfoKey,
          regions: [REGIONS.NA],
          rightExtra: <Text style={styles.rightExtraText} />,
          onItemClicked: () => {
            tracer.click({
              eleid: eleIdMap.Product_Information_Button_Click,
            });
            const route = 'ChervonIot://EGO/DeviceManage/productHelp';
            const params = {
              productId,
            };
            JumpUtils.jumpTo(route, params);
          },
        },
        {
          icon: require('@assets/list/61004_list_icon_infor.png'),
          title: Strings.getLang(commonAboutKey),
          key: commonAboutKey,
          regions: [REGIONS.NA, REGIONS.EU],
          rightExtra: <Text style={styles.rightExtraText} />,
          onItemClicked: () => {
            tracer.click({
              eleid: eleIdMap.Equipent_Information_Button_Click,
            });
            goPage(navigation, 'ViewDeviceMsg', {detail});
          },
        },
        {
          icon: require('@assets/list/61004_list_parts.png'),
          title: Strings.getLang(commonPartsKey),
          key: commonPartsKey,
          regions: [REGIONS.NA],
          rightExtra: <Text style={styles.rightExtraText} />,
          onItemClicked: () => {
            tracer.click({
              eleid: eleIdMap.Enclosure_Button_Click,
            });
            jumpToParts({
              productId: productId,
              deviceId: deviceId,
            });
          },
        },
        {
          icon: require('@assets/list/61004_list_share_device.png'),
          title: Strings.getLang(commonShareDeviceKey),
          key: commonShareDeviceKey,
          regions: canBeShared ? [REGIONS.NA, REGIONS.EU] : [],
          rightExtra: <Text style={styles.rightExtraText} />,
          onItemClicked: () => {
            tracer.click({
              eleid: eleIdMap.Share_Device_Click,
            });
            const deviceName = detail?.deviceName || '';
            const deviceIcon = detail?.deviceIcon || '';
            const route = 'ChervonIot://EGO/UserCenter/shareDeviceDetail';
            const params = {
              deviceId,
              deviceName,
              deviceIcon,
            };
            JumpUtils.jumpTo(route, params);
          },
        },
        {
          icon: require('@assets/list/61004_list_icon_feedback.png'),
          title: Strings.getLang(commonFeedBackKey),
          key: commonFeedBackKey,
          regions: [REGIONS.NA, REGIONS.EU],
          rightExtra: <Text style={styles.rightExtraText} />,
          onItemClicked: () => {
            const {commodityModel, sn} = detail;
            tracer.click({
              eleid: eleIdMap.FeedBack_Button_Click,
            });
            JumpUtils.jumpTo('ChervonIot://EGO/UserCenter/feedbackPublish', {
              sourceType: 'RN',
              productId,
              deviceId,
              nickName,
              commodityModel,
              sn,
            });
          },
        },
      ];
    }, [
      rootStore.deviceStore.actions,
      detail,
      hasUnreadMessage,
      showRed,
      navigation,
      getDetailList,
      bleConnected,
      productId,
      wifiName,
      showModal,
      deviceId,
      connected,
      nickName,
    ]);

    // 过滤后的数据源
    const filteredBaseData = useMemo(() => {
      const {region} = rootStore.deviceStore.actions;
      return baseDataSource.filter(item =>
        item.regions.includes(region as RegionType),
      );
    }, [baseDataSource, rootStore.deviceStore.actions]);

    useEffect(() => {
      setBaseData(filteredBaseData);

      const handleFocus = () => {
        getDetailList();
        getWhetherDeviceHasUnreadMessage();
      };

      CommonEmitter.addListener('NAVIGATOR_ON_WILL_FOCUS', handleFocus);

      return () => {
        CommonEmitter.removeAllListeners('NAVIGATOR_ON_WILL_FOCUS');
      };
    }, [filteredBaseData, getDetailList, getWhetherDeviceHasUnreadMessage]);

    useEffect(() => {
      // 设备详情页曝光埋点
      tracer.page();
      getDetailList();
      // 发送指令获取wifi信息
      rootStore.deviceConnectionStore.actions.getDeviceWifiInfo();
      getWhetherDeviceHasUnreadMessage();
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    // 监听页面失去焦点（相当于页面消失）
    useFocusEffect(
      React.useCallback(() => {
        // 页面获得焦点时的逻辑（可选）
        return () => {
          // 页面失去焦点时的逻辑（导航离开、切换标签页等）
          tracer.leave({pageid: pageIdMap.detailList.toString()});
        };
      }, []),
    );

    const deleteDevice = useCallback(() => {
      deviceUnbind({
        deviceId: deviceId,
      })
        .then(() => {
          onHideDetailListAlert('deleteSuccess');
          rootStore.deviceConnectionStore.actions.setIsNativeGoback(true);
          mobile.back();
        })
        .catch(() => {
          onHideDetailListAlert('deleteFail');
        });
    }, [deviceId, rootStore.deviceConnectionStore.actions]);

    const resetPasswordBeforeDelete = useCallback(() => {
      deviceResetPassword({
        deviceId: deviceId,
        identifier: DEVICE_RESET_IDENTIFIER,
        value: true,
      })
        .then(() => {
          deleteDevice();
        })
        .catch(() => {
          onHideDetailListAlert('deleteFail');
        });
    }, [deviceId, deleteDevice]);

    /**
     * 解绑设备
     */
    const unBindDevice = useCallback(() => {
      onShowDetailListAlert();
      tracer.page({pageid: pageIdMap.detailListAlert.toString()});
      mobile.simpleConfirmDialog(
        Strings.getLang('rn_common_detaillist_alerttitle_textview_text'),
        Strings.getLang(
          rootStore.deviceStore.actions.canBeShared
            ? 'rn_common_detaillist_alertmessage_deviceShare_textview_text'
            : 'rn_common_detaillist_alertmessage_textview_text',
        ),
        () => {
          const {deviceDetail} = rootStore.deviceStore.state;
          const {shareType} = deviceDetail;
          onHideDetailListAlert('confirm');
          // 主账户 删除设备前需要重置密码
          if (shareType === SHARE_TYPE.MAIN_ACCOUNT) {
            resetPasswordBeforeDelete();
          } else {
            deleteDevice();
          }
        },
        () => {
          onHideDetailListAlert('cancel');
        },
      );
    }, [
      rootStore.deviceStore.actions.canBeShared,
      rootStore.deviceStore.state,
      resetPasswordBeforeDelete,
      deleteDevice,
    ]);

    return (
      <PageView>
        <HeaderView
          title={Strings.getLang('rn_common_detaillist_title_textview_text')}
          useCommonEleId={false}
          onLeftPress={onLeftPress}
        />
        <ScrollView showsVerticalScrollIndicator={false}>
          <BaseCard
            connected={connected}
            data={baseData}
            bleConnected={bleConnected}
          />
          <Button
            onPress={unBindDevice}
            title={Strings.getLang(
              'rn_common_detaillist_deletedevice_button_text',
            )}
            containerStyle={styles.buttonContainer}
            buttonStyle={styles.button}
            titleStyle={styles.buttonText}
          />
          <WhiteSpace size={isIphoneX ? 34 : 10} />
        </ScrollView>
      </PageView>
    );
  }),
);

export default memo(List);

export const BaseCard = memo(
  ({data, connected = false, bleConnected = false}: BaseCardProps) => {
    return (
      <View style={styles.featureContainer}>
        {data.map((item: ListItem, index: number) => {
          let newDisable = false;
          if (item?.key === commonUpgradeKey) {
            newDisable = !connected && !bleConnected;
          }
          return (
            <Item
              disabled={newDisable}
              leftIconSource={item.icon}
              leftIconStyle={styles.leftIcon}
              onPress={() => {
                item?.onItemClicked();
              }}
              hideLine={index === data.length - 1}
              key={item.title}
              title={item.title}
              titleStyle={styles.title}
              rightExtra={item.rightExtra}
            />
          );
        })}
      </View>
    );
  },
);
const styles = StyleSheet.create({
  heading: {
    color: 'white',
    fontSize: 22,
    fontWeight: 'bold',
  },
  headerLeft: {
    display: 'flex',
    flexDirection: 'row',
    marginTop: 5,
  },
  headerRight: {
    display: 'flex',
    flexDirection: 'row',
    marginTop: 5,
  },
  featureContainer: {
    marginTop: 10,
    borderRadius: 10,
    marginHorizontal: 15,
    overflow: 'hidden',
  },
  rightExtraText: {
    color: '#666666',
    fontSize: 13,
  },
  deviceName: {
    width: DEVICE_WIDTH - 135 - 66 - 30,
    textAlign: 'right',
  },
  registerText: {
    color: '#77BC1F',
    fontSize: 13,
  },
  redDot: {
    borderRadius: 6.5,
    width: 13,
    height: 13,
    backgroundColor: '#EC6464',
  },
  buttonContainer: {
    marginHorizontal: 15,
    height: 48,
    marginTop: 10,
    borderRadius: 10,
  },
  button: {
    backgroundColor: '#ffffff',
    borderRadius: 2,
    justifyContent: 'flex-start',
    paddingLeft: 23,
    height: 50,
  },
  buttonText: {
    color: '#77BC1F',
    fontSize: 15,
  },
  // card
  leftIcon: {
    width: 28,
    height: 28,
  },
  title: {
    marginLeft: 0,
  },
});
