/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2023-04-14 15:53:38
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-02-04 19:01:25
 * @FilePath: /61004/src/pages/detail/utils
 * @Description: 设备详情，埋点事件封装
 */
import {Utils} from 'cvn-panel-kit';
import {tracer, eleIdMap, pageIdMap} from '@tracer';
const {LogUtils} = Utils;

export const onShowDetailListAlert = () => {
  LogUtils.setNotRouteChangeAndWillShow(() => {
    tracer.click({
      eleid: eleIdMap.Delete_Device_Button_Click,
    });
    const pageId = pageIdMap.detailListAlert;
    tracer.leave({pageid: pageId.toString()});
    tracer.page({pageid: pageId});
  }, true);
};
/**
 * @description: 关闭对话框埋点逻辑
 */
export const onHideDetailListAlert = (type = 'cancel') => {
  LogUtils.setNotRouteChangeAndWillShow(() => {
    if (type === 'cancel') {
      tracer.click({
        eleid: eleIdMap.Detail_List_Alert_Cancel_Button_Click,
        pageid: pageIdMap.detailListAlert,
      });
    } else if (type === 'confirm') {
      tracer.click({
        eleid: eleIdMap.Confirm_Button_Click,
        pageid: pageIdMap.detailListAlert,
      });
    } else if (type === 'deleteSuccess') {
      tracer.click({
        eleid: eleIdMap.Remove_Device_Success,
        pageid: pageIdMap.detailListAlert,
      });
    } else if (type === 'deleteFail') {
      tracer.click({
        eleid: eleIdMap.Remove_Device_Fail,
        pageid: pageIdMap.detailListAlert,
      });
    }
    // 接口返回后有处理，不需重复
    if (type !== 'confirm') {
      tracer.leave({pageid: pageIdMap.detailListAlert.toString()});
      tracer.page({pageid: pageIdMap.detailList});
    }
  }, false);
};

export default onHideDetailListAlert;
