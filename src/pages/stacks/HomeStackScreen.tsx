/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2024-02-20 17:59:05
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-07-02 19:18:27
 * @FilePath: /61004/src/pages/stacks/HomeStackScreen
 * @Description:
 *
 */
import React, {memo, useEffect} from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import {inject, observer} from 'mobx-react';
import Routes from '../Route';
import {RootStore} from 'IOTRN/src/mobx/rootStore';
import {InitialParamsProps} from 'IOTRN/src/types/device';
const HomeStack = createStackNavigator();
interface Props {
  rootStore?: RootStore;
  props?: InitialParamsProps;
}

const HomeStackScreen = inject('rootStore')(
  observer(({rootStore, ...props}: Props) => {
    useEffect(() => {
      rootStore?.deviceStore?.actions?.initParams?.({
        deviceId: '',
        mac: '',
        deviceName: '',
        appSettingOfHour: 0,
        region: '',
        timezone: '',
        productId: '',
        deviceDetail: '',
        appSettingOfUnit: '',
        ...props,
      });
    }, [rootStore?.deviceStore?.actions, props, rootStore]);
    const names = Object.keys(Routes);
    // StackNavigator栈的个数暂时和tab数量一致
    return (
      <HomeStack.Navigator
        screenOptions={() => {
          return {
            headerShown: false,
            // 可以控制手势返回，暂时全部关闭
            gestureEnabled: false,
          };
        }}>
        {names.map(name => {
          const Component = Routes[name];
          return (
            <HomeStack.Screen
              key={name}
              name={name}
              component={Component}
              options={{title: name}}
            />
          );
        })}
      </HomeStack.Navigator>
    );
  }),
);

export default memo(HomeStackScreen);
