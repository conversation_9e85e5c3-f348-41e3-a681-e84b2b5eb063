/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2023-10-12 14:04:33
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-07-17 16:25:12
 * @FilePath: /61004/src/pages/Route
 * @Description: 路由
 */
// /* eslint-disable import/max-dependencies */
import {
  ViewPanelHome,
  ViewDetailList,
  ViewDeviceMsg,
  ViewEditName,
  ViewUsageHistory,
  ViewBattery,
  ViewStatistics,
} from '@pages/index';

const Routes: {
  [key: string]: React.ComponentType;
} = {
  ViewPanelHome: ViewPanelHome as unknown as React.ComponentType,
  ViewDetailList: ViewDetailList as unknown as React.ComponentType,
  ViewDeviceMsg: ViewDeviceMsg as unknown as React.ComponentType,
  ViewEditName: ViewEditName as unknown as React.ComponentType,
  ViewUsageHistory: ViewUsageHistory as unknown as React.ComponentType,
  ViewBattery: ViewBattery as unknown as React.ComponentType,
  ViewStatistics: ViewStatistics as unknown as React.ComponentType,
};

// 定义 router
export default Routes;
