import commonStrings from './common';

export const newAddedMap = {
  en: {
    rn_61004_panelhome_connected_textview_text: 'Connected',
    rn_61004_panelhome_disconnected_textview_text: 'Disconnected',
    rn_61004_panelhome_connecting_textview_text: 'Connecting',
    rn_61004_panelhome_accessories_textview_text: 'Accessories',

    rn_61004_panelhome_usagehistory_textview_text: 'Usage History',

    rn_61004_panelhome_usagedetailsnodata_textview_text: 'No Content.',

    rn_61004_panelhome_updatetitle_textview_text: 'Up To Date',
    rn_61004_panelhome_partsmaintenance_textview_text: 'maintenance required',
    rn_61004_panelhome_partsavailable_textview_text: 'available',
    rn_61004_panelhome_registinfo_textview_text: 'Register Info',

    rn_61004_panelhome_percentunit_textview_text: '%',
    rn_61004_panelhome_setpasswordtitle_textview_text: 'Set Password',
    rn_61004_panelhome_setpasswordsubtitle_textview_text:
      'please set a 4-digit device password',
    rn_61004_panelhome_setpasswordconfirm_textview_text: 'Confirm',
    rn_61004_panelhome_hour_textview_text: 'hr',
    rn_61004_panelhome_minute_textview_text: 'min',
    rn_61004_panelhome_powerconsumption_textview_text: 'Ah',
    rn_61004_panelhome_distancekilometers_textview_text: 'km',
    rn_61004_panelhome_bladspeed_unit_textview_text: 'RPM',
    rn_61004_panelhome_otaupgrade_textview_text: 'Upgrade',
    rn_61004_panelhome_otacancel_textview_text: 'Cancel',

    // 使用详情页面
    rn_61004_usagehistory_title_textview_text: 'Usage History',

    rn_61004_usagehistory_day_textview_text: 'Day',
    rn_61004_usagehistory_week_textview_text: 'Week',
    rn_61004_usagehistory_month_textview_text: 'Month',

    rn_61004_usagehistory_kgunit_textview_text: 'kg',
    rn_61004_usagehistory_m2unit_textview_text: '㎡',
    rn_61004_usagehistory_acresunit_textview_text: 'acres',
    rn_61004_usagehistory_milesunit_textview_text: 'miles',
    rn_61004_usagehistory_unitkilometers_textview_text: 'km',
    rn_61004_usagehistory_lbsunit_textview_text: 'lbs',
    rn_61004_usagehistory_hour_textview_text: 'hr',
    rn_61004_usagehistory_minute_textview_text: 'min',
    rn_61004_usagehistory_h_textview_text: 'h',

    rn_61004_usagehistory_alerttotaltime_textview_text: 'Total mowing hours',
    rn_61004_usagehistory_alerttotalmowingarea_textview_text:
      'Total mowing area',
    rn_61004_usagehistory_alerttotalpowerconsumption_textview_text:
      'Total power consumption',
    rn_61004_usagehistory_alerttotaldrivingdistance_textview_text:
      'Total driving distance',
    rn_61004_usagehistory_alerttotalco2reductione_textview_text:
      'Total CO₂ reduction',
    rn_61004_usagehistory_alertco2reduction_textview_text: 'CO₂ Reduction',
    rn_61004_usagehistory_alertmowingtime_textview_text: 'Mowing Time',
    rn_61004_usagehistory_alertmowingarea_textview_text: 'Mowing Area',
    rn_61004_usagehistory_alertpowerconsumption_textview_text:
      'Power Consumption',
    rn_61004_usagehistory_alertdrivingdistance_textview_text:
      'Driving Distance',

    rn_61004_usagehistory_cardtitletotalworkingtime_textview_text:
      'Total working time',
    rn_61004_usagehistory_cardtitletotalco2reduction_textview_text:
      'Total CO₂ reduction',
    rn_61004_usagehistory_cardtitletotalmowingarea_textview_text:
      'Total mowing Area',
    rn_61004_usagehistory_datepickertitle_textview_text: 'Date picker',
    // pannel/home
    rn_61004_panelhome_otaalertmsg_textview_text:
      'if you do not upgrade the device will not work properly,Please upgrade now',
    // deviceList
    rn_61004_detaillist_wificonfig_textview_text: 'Wi-Fi Configuration',
    rn_tractor_panelhome_otaalertmsg_textview_text:
      'if you do not upgrade the device will not work properly,Please upgrade now',
    rn_61004_detaillist_device_notification_textview_text:
      'Device Notifications',
    rn_61004_detaillist_share_device_textview_text: 'Share Device',
    rn_common_detaillist_feedback_textview_text: 'Feedback',
    rn_61004_detaillist_devicestatus_textview_text: 'Device Status',
    rn_61004_detaillist_changepassword_textview_text: 'Change Password',
    rn_61004_remotecontrol_title_textview_text: 'Remote Control',
    rn_61004_remotecontrol_bladeisopentip_textview_text:
      'Please TURN OFF the blade！',
    rn_61004_remotecontrol_seathaspeopletip_textview_text:
      'DO NOT sit in the seat during remote control！',
    rn_61004_remotecontrol_outofrangetip_textview_text:
      'Please turn on the device and stand within 15 feet from the device',

    // z6
    rn_61004_panelhome_runningspeed_textview_text: 'Running Speed',
    rn_61004_panelhome_runtimeremaining_textview_text: 'Runtime Remaining',
    rn_61004_panelhome_bladerotation_textview_text: 'Blade Rotation',
    rn_61004_panelhome_registration_textview_text: 'Registration Info',
    rn_61004_panelhome_registernow_textview_text: 'Register Now',
    rn_61004_common_detaillist_usermanual_textview_text: 'Product Information',
    rn_61004_panelhome_otaver_textview_text: 'Ver: ',
    rn_61004_panelhome_upgradeavailable_textview_text: 'Upgrade Available',
    rn_61004_panelhome_upgradedate_textview_text: 'Up to Date',
    rn_61004_panelhome_mowingperfomance_textview_text:
      'Mowing Performance Mode',
    rn_61004_panelhome_mowingstardard_textview_text: 'STANDARD',
    rn_61004_panelhome_mowinghyper_textview_text: 'HYPER',
    rn_61004_panelhome_backupalert_textview_text: 'Back Up Alert',
    rn_61004_panelhome_daytimelight_textview_text: 'Daytime Light',
    rn_61004_panelhome_off_textview_text: 'OFF',
    rn_61004_panelhome_running_textview_text: 'RUNNING',
    rn_61004_panelhome_on_textview_text: 'ON',
    rn_tractor_panelhome_lightdelay_textview_text: 'Light-off Delay',
    rn_tractor_panelhome_willturnoff_textview_text: 'Will turn off in ',
    rn_61004_panelhome_butteryInfo_textview_text: 'Battery remaining',
    rn_61004_panelhome_vehicalunlocked_textview_text: 'Vehicle Unlocked',
    rn_61004_panelhome_statics_driving_distance_textview_text:
      'Total driving distance',
    rn_61004_panelhome_statics_power_consumption_textview_text:
      'Total power consumption',
    rn_61004_panelhome_statics_total_co2reduce_textview_text:
      'Total CO₂ emmission reduced',
    rn_61004_panelhome_mowingstandard_description_textview_text:
      'Standard mode balances cutting efficiency with extended runtime, ideal for daily use.',
    rn_61004_panelhome_mowinghyper_description_textview_text:
      'Hyper Mode offers superior cutting power at higher speeds, but with limited runtime.',
    rn_61004_panelhome_keylock_description_textview_text:
      "If you don't have a physical key with you, you can use the EGO Connect app to unlock.",
    rn_61004_panelhome_remoteunlock_description_textview_text:
      'Vehicle is unlocked via app, device will lock again when powered off.',
    rn_61004_panelhome_usagehistory_help_description_textview_text1:
      '1. ‘Usage History’ data is stored on the tool; please connect the tool to this app to synchronize and view updated usage history data.',
    rn_61004_panelhome_usagehistory_help_description_textview_text2:
      '2. ‘Total Time’values reflect usage starting from the first time the tool is used, while monthly usage data as shown on the graph only shows data starting from the first time the tool is connected to this app.',
    rn_61004_remotecontrol_remainingbattery_textview_text: 'Remaining Battery',
    rn_61004_remotecontrol_vehiclespeed_textview_text: 'Vehicle speed',

    rn_61004_remotecontrol_mphunit_textview_text: 'mph',
    rn_61004_remotecontrol_devicewithinrange_textview_text:
      'Device within range',
    rn_61004_remotecontrol_deviceoutofrange_textview_text:
      'Device out of range',
    rn_61004_remotecontrol_bledisconnected_textview_text: 'BLE Disconnected',
    rn_61004_wificonfig_bledisconnected_textview_text:
      'Please connect your phone and device via Bluetooth to configure the Wi-Fi network.',
    rn_61004_trackhistory_title_textview_text: 'Tracks History',
    rn_61004_trackhistory_cuttingarea_textview_text: 'Cutting area',
    rn_61004_trackhistory_cuttingtime_textview_text: 'Cutting time',
    rn_61004_trackhistory_averagespeed_textview_text: 'Average speed',
    rn_61004_trackhistory_maxspeed_textview_text: 'Max speed',
    rn_61004_trackhistory_drivingdistance_textview_text: 'Driving distance',
    rn_61004_trackhistory_acreunit_textview_text: 'acre',
    rn_61004_trackhistory_minunit_textview_text: 'min',
    rn_61004_trackhistory_mphunit_textview_text: 'mph',
    rn_61004_trackhistory_milesunit_textview_text: 'miles',
    rn_61004_trackhistory_m2unit_textview_text: '㎡',
    rn_61004_trackhistory_hrunit_textview_text: 'hr',
    rn_61004_trackhistory_kmperhunit_textview_text: 'km/h',
    rn_61004_trackhistory_dailyusagetitle_textview_text: 'Daily usage:',
    rn_61004_trackhistory_selectedtimetoast_textview_text:
      'The selection time cannot exceed today',
    rn_61004_trackhistory_datepickertitle_textview_text: 'Date picker',

    rn_61004_devicelocation_title_textview_text: 'Device Location',
    rn_61004_devicelocation_direction_button_text: 'Direction',
    rn_61004_devicelocation_trackhistory_button_text: 'Tracks History',
    rn_61004_devicelocation_permissionnotgranted_button_text:
      'Location permission is not granted',

    rn_61004_panelhome_remotecontrol_textview_text: 'Remote Control',
    rn_61004_panelhome_devicelocation_textview_text: 'Device Location',

    rn_61004_panelhome_devicestatus_textview_text: 'Device Status',
    rn_61004_panelhome_totalworkingtime_textview_text: 'Total driving hours',
    rn_61004_panelhome_totalmowingtime_textview_text: 'mowing time',
    rn_61004_panelhome_batterycardtitle_textview_text: 'Battery',
    rn_61004_panelhome_slopecardtitle_textview_text: 'Steep Slope Attention',
    rn_61004_panelhome_slopecarddegree15_textview_text: '15°',
    rn_61004_panelhome_slopecarddegree20_textview_text: '20°',
    rn_61004_panelhome_slopecarddegree25_textview_text: '25°',
    rn_61004_panelhome_statuscharging_textview_text: 'Charging',
    rn_61004_panelhome_statusmowing_textview_text: 'Mowing',
    rn_61004_panelhome_statuspowerandcharger_textview_text: 'Power+ Charger',
    rn_61004_panelhome_statuscardfinishin_textview_text: 'Finishing in',
    rn_61004_panelhome_statuscardmowingprefix_textview_text:
      'Time remaining to mow',
    rn_common_ble_reconnecterror_textview_text: 'Bluetooth connection failed',
    rn_61004_batteryinfo_title_textview_text: 'Battery Information',
    rn_61004_batteryremain_title_textview_text: 'Battery remaining',

    rn_61004_devicestatus_title_textview_text: 'Device status',
    rn_61004_devicestatus_noerror_textview_text: 'All Modules Operational',

    rn_61004_changepassword_title_textview_text: 'Change Password',
    rn_61004_changepassword_save_textview_text: 'Save',
    rn_61004_changepassword_resetpwd_textview_text: 'Reset Password',
    rn_61004_changepassword_currentpassword_textview_text: 'Current password:',
    rn_61004_changepassword_newpassword_textview_text: 'New password:',
    rn_61004_changepassword_alerttitle_textview_text:
      'Do you want to reset the password?',
    rn_61004_changepassword_aleryes_textview_text: 'Yes',
    rn_61004_changepassword_alerno_textview_text: 'No',
    rn_61004_changepassword_savesuccess_textview_text: 'Success',
    rn_61004_changepassword_savefail_textview_text: 'Failed',
    rn_61004_changepassword_resetsuccess_textview_text: 'Success',
    rn_61004_changepassword_resetfail_textview_text: 'Failed',
  },
};

/**
 * 本地多语言，如需要网络动态多语言，需要merge
 */
export default {
  en: {
    ...commonStrings.en,
    ...newAddedMap.en,
  },
  zh: {
    ...commonStrings.zh,
  },
};
