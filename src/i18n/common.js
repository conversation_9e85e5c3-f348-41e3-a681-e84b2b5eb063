/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2023-02-14 16:03:48
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-09-13 19:09:42
 * @FilePath: /61004/src/i18n/common
 * @Description: 通用多语言
 */
export default {
  en: {
    rn_common_operation_success_textview_text: 'Operation success',
    rn_common_net_error_textview_text: 'Network disconnected',
    rn_common_ble_error_textview_text: 'Bluetooth disconnected',

    rn_common_ble_opened_textview_text: 'Bluetooth is on',
    rn_common_ble_notopened_textview_text:
      'Bluetooth is not enabled. Please enable Bluetooth before using',
    rn_common_ble_notauthorizedtitle_textview_text: 'Bluetooth not authorized',
    //
    rn_common_ble_retryconnect_textview_text: 'Trying to reconnect',

    // base common
    rn_common_nodata_title_textview_text: 'No Data',
    rn_common_welcome_agree_button_text: 'Agree & Continue',
    rn_common_welcome_disagree_button_text: 'Disagree',

    // 单位统一
    rn_common_unit_km_textview_text: 'km',
    rn_common_unit_hour_textview_text: 'h',
    // 3.16修改
    rn_common_unit_weight_textview_text: 'kg',

    // editname
    rn_common_deviceeditname_title_textview_text: 'Rename device',
    rn_common_deviceeditname_desc_textview_text: 'Please enter 1-20 charcaters',
    rn_common_deviceeditname_placeholder_input_text: 'input name',
    rn_common_deviceeditname_save_button_text: 'Save',
    rn_common_deviceeditname_notallowempty_button_text:
      'Nickname cannot be empty',

    // deviceMsg
    // 9.10日参照 泉峰最新的静态多语言修改(标准有变化导致)
    rn_common_devicemsg_title_textview_text: 'Device Information',
    rn_common_devicemsg_modelno_textview_text: 'Model Number',
    rn_common_devicemsg_sn_textview_text: 'Serial Number',
    rn_common_devicemsg_deviceid_textview_text: 'Device ID',
    rn_common_devicemsg_assemblysn_textview_text: 'Assembly Serial Number',
    rn_common_devicemsg_version_textview_text: 'Firmware Version',

    // detail/list
    rn_common_detaillist_title_textview_text: 'Device Details',
    rn_common_detaillist_devicename_textview_text: 'Device Name',
    rn_common_detaillist_registration_textview_text: 'Registration',
    rn_common_detaillist_upgrade_textview_text: 'Update',
    rn_common_detaillist_statistics_textview_text: 'Statistics',
    rn_common_detaillist_productintro_textview_text: 'Product Information',
    rn_common_detaillist_about_textview_text: 'About',
    rn_common_detaillist_parts_textview_text: 'Accessories',
    rn_common_detaillist_privacy_textview_text: 'Privacy',
    rn_common_detaillist_deletedevice_button_text: 'Delete device',
    rn_common_detaillist_registered_textview_text: 'Registered',
    rn_common_detaillist_unregistered_textview_text: 'Unregistered',
    rn_common_detaillist_alerttitle_textview_text:
      'Are you sure to remove the device？',
    rn_common_detaillist_alertmessage_textview_text:
      'Your device settings will be deleted if this device is removed',
    rn_common_detaillist_alertmessage_deviceShare_textview_text:
      'Your device settings will be deleted and all shared accounts will be removed',
    // 12.14日新增
    rn_common_deviceeditname_notspecialcharacter_textview_text:
      'Cannot contain special characters',
    rn_common_unit_kmperh_textview_text: 'km/h',
    rn_common_unit_squaremeter_textview_text: '㎡',
    rn_common_unit_meter_textview_text: 'm',
    rn_common_unit_squaremeterperhour_textview_text: '㎡/h',
    rn_common_unit_kwh_textview_text: 'kwh',
    // 2.15日
    rn_common_ble_connected_textview_text: 'Bluetooth connected',
    // 2.25新增
    rn_common_unit_cm_textview_text: 'cm',
    rn_common_unit_mm_textview_text: 'mm',
    rn_common_unit_inch_textview_text: 'inch',
    rn_common_unit_min_textview_text: 'min',
    // 2.27日新增
    rn_common_devicemsg_rnversion_textview_text: 'RN Version',
    rn_common_unit_ah_textview_text: 'Ah',
    // 3.2日新增
    rn_common_unit_kg_textview_text: 'kg',
    // 3.6日
    rn_common_ble_5therror_textview_text:
      'Connection failed. Please make sure that Bluetooth is turned on and in communication range for both the device and the phone.',
    rn_common_editalert_cancel_textview_text: 'Cancel',
    rn_common_editalert_done_textview_text: 'Done',
    rn_common_editalert_placeholder_textview_text: 'Enter new name',
    // 3.9日新增
    rn_common_unit_co2kg_textview_text: 'kg',
    // 5.31
    rn_common_permission_unavailable_textview_text:
      'Positioning function not available',
    rn_common_permission_limited_textview_text:
      'Localization function limitations',
    rn_common_permission_blocked_textview_text:
      'Location permission is not granted, some functions are restricted to use',
  },
  zh: {},
};
