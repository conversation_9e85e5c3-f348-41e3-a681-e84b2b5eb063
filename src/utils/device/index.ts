/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2024-01-16 15:03:24
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-07-03 20:20:34
 * @FilePath: /61004/src/utils/device/index
 * @Description: 设备相关信息
 */

import {Dimensions, PixelRatio, Platform, StatusBar} from 'react-native';
import Device from 'react-native-device-info';
import {mobile} from 'cvn-panel-kit';

const isiOS = Platform.OS === 'ios';

const isStatusBarHeightGreaterOrEqualTo20 = StatusBar.currentHeight
  ? StatusBar.currentHeight >= 20
  : false;

const sRatio = PixelRatio.get();
const dimen = Dimensions.get('window');

// ipod 7 width: 0 ?
const DEVICE_WIDTH = dimen.width;
const DEVICE_HEIGHT = dimen.height;

// 屏幕比例
const Ratio = sRatio ? parseInt(sRatio.toString(), 10) : 2;
// 2024.1.8日更新，修改了对iPhoneX的判断
const {isIPhoneX: isIphoneX} = mobile;

// 是否是齐刘海
const isHasNotch = Device.hasNotch();
const isNotch = isHasNotch;
// 14pro
// 宽高：393*852
// 14promax
// 宽高：430*932
// 428*926  12promax
const hasHole =
  (DEVICE_WIDTH === 393 && DEVICE_HEIGHT === 852) ||
  (DEVICE_WIDTH === 430 && DEVICE_HEIGHT === 932);
const NavgationBarHeight = 44;
const SafeArea = {
  top: isHasNotch ? 44 : 20,
  bottom: isHasNotch ? 34 : 0,
};

// 状态栏高度
export const getStatusBarHeight = () => {
  if (isiOS) {
    return isNotch ? 44 : 20;
  } else {
    return isStatusBarHeightGreaterOrEqualTo20 ? StatusBar.currentHeight : 24;
  }
};

const scale = Dimensions.get('window').scale;
export {
  DEVICE_WIDTH,
  DEVICE_HEIGHT,
  Ratio,
  isIphoneX,
  isHasNotch,
  NavgationBarHeight,
  SafeArea,
  scale,
  hasHole,
};
