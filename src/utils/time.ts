/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2023-12-28 19:11:13
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-08-08 13:40:42
 * @FilePath: /61004/src/utils/time
 * @Description: time 相关库
 */
import dayjs from 'dayjs';
import Strings from '@i18n';
export const getTimeArray = (timestamp2: number, count: number) => {
  const timeArray = [];
  const interval = 10 * 60 * 1000;
  for (let i = 0; i < count; i++) {
    const time = new Date(timestamp2 - interval * i);
    const hours = time.getHours().toString().padStart(2, '0');
    const minutes = time.getMinutes().toString().padStart(2, '0');
    const timeStr = `${hours}:${minutes}`;
    timeArray.unshift(timeStr);
  }

  return timeArray;
};

/**
 * @param {Number} seconds
 * @returns {String} 首页折线图上方使用
 */
export const formatHomeTime = (seconds: number) => {
  const hoursValue = Math.floor(seconds / 3600);
  const minutesValue = Math.floor((seconds % 3600) / 60);
  const hours = hoursValue.toFixed(0).padStart(2, '0');
  const minutes = minutesValue.toFixed(0).padStart(2, '0');
  return (
    hours +
    `${Strings.getLang('rn_61004_panelhome_hour_textview_text')}` +
    ' ' +
    minutes +
    Strings.getLang('rn_61004_panelhome_minute_textview_text')
  );
};
/**
 *
 * @param {*} seconds
 * @returns {Object} hour,minute
 */
export const convertSecondsToTimeInfo = (seconds: number) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  return {
    hour: hours.toFixed(0).padStart(2, '0'),
    minute: minutes.toFixed(0).padStart(2, '0'),
  };
};
/**
 * @description 将月份全称转换为简称
 * @param {String} fullName
 * @returns String
 */
export const convertMonthName = (fullName = '') => {
  const fullNameUpper = fullName.toUpperCase();
  const months: {[key: string]: string} = {
    JANUARY: 'Jan',
    FEBRUARY: 'Feb',
    MARCH: 'Mar',
    APRIL: 'Apr',
    MAY: 'May',
    JUNE: 'Jun',
    JULY: 'Jul',
    AUGUST: 'Aug',
    SEPTEMBER: 'Sep',
    OCTOBER: 'Oct',
    NOVEMBER: 'Nov',
    DECEMBER: 'Dec',
    'THIS MONTH': 'This,month',
  };
  return months[fullNameUpper];
};
// 判断是否是今天
export const isToday = (dayStr = '') => {
  const dateToCheck = dayjs(dayStr);
  const today = dayjs();
  return dateToCheck.isSame(today, 'day');
};
// 判断目标值 2024/05/16  是否是大于今天
export const isAfterToday = (selectedTime = '') => {
  const selectedDay = dayjs(selectedTime, {format: 'YYYY/MM/DD'});
  const today = dayjs();
  let result = false;
  if (selectedDay.isAfter(today, 'day')) {
    result = true;
  }
  return result;
};
// 判断目标值 2024/05/16  是否是小于今天
export const isBeforeToday = (selectedTime = '') => {
  const selectedDay = dayjs(selectedTime, {format: 'YYYY/MM/DD'});
  const today = dayjs();
  let result = false;
  if (selectedDay.isBefore(today, 'day')) {
    result = true;
  }
  return result;
};
/**
 * @description: 变量 minutes，当大于60时，格式化成  xx hr xx min,小于60时， xx min
 * @param {Number} minutes
 * @example: 70 --> '01 hr 10 min'; 50 --> '50 min'
 * @returns 格式化后字符串
 */
export const formatMinutes = (minutes = 0) => {
  if (minutes > 60) {
    const hours = Math.floor(minutes / 60)
      .toString()
      .padStart(2, '0');
    const remainingMinutes = (minutes % 60).toString().padStart(2, '0');
    return `${hours} ${Strings.getLang(
      'rn_61004_trackhistory_hrunit_textview_text',
    )} ${remainingMinutes} ${Strings.getLang(
      'rn_61004_trackhistory_minunit_textview_text',
    )}`;
  } else {
    return `${minutes} ${Strings.getLang(
      'rn_61004_trackhistory_minunit_textview_text',
    )}`;
  }
};
