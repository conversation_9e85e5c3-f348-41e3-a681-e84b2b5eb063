/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2023-02-09 11:35:36
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-09-11 10:25:14
 * @FilePath: /61004/src/utils/tracer/moduleIdMap
 * @Description: 埋点的moduleId映射列表
 */
import {MODULE_IDS} from './moduleConstants';

const moduleIdMap = {
  /**
   * 首页、使用数据、设备离线对话框
   */
  panelHome: MODULE_IDS.PANEL_HOME, // 首页模块
  /**
   * 包含设备详情、设备信息、移除设备对话框、设备名字编辑、遥控页面、设备定位地图页、使用历史、修改设备密码页
   */
  detailList: MODULE_IDS.DETAIL_LIST,
};

export default moduleIdMap;
