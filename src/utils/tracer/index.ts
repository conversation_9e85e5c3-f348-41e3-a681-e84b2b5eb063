import {Utils} from 'cvn-panel-kit';
import pageIdMap from './pageIdMap';
import eleIdMap from './eleIdMap';
import moduleIdMap from './moduleIdMap';
import durationMap from './durationMap';
import {parseJsonSafely} from '../jsonParser';
const {TracerUtils, LogUtils} = Utils;
const {mobileTrace, getInfoStr, getEventIdFromModuleIdAndInfo} = TracerUtils;
import {isNumber} from 'lodash-es';
/**
 * @description: 添加moduleid并调用公共的mobileTrace
 * @param {String} infoStr
 */
export const addKeyValueAndTrace = (infoStr: string) => {
  // 使用安全的JSON解析，提供默认值和错误处理
  const info = parseJsonSafely(
    infoStr,
    {
      pageid: '',
      expand: {},
    } as {
      pageid?: string;
      expand?: {[key: string]: string};
    }, // 默认空对象
    'TracerUtils.addKeyValueAndTrace',
  );

  const {pageid = ''} = info;
  const moduleid = getModuleIdFromPageId(pageid);
  const eventid = getEventIdFromModuleIdAndInfo(moduleid, info);

  const formatExpand = getFormatExpand({
    ...info,
    expand: info.expand ?? {},
  });
  const newInfo = {
    ...info,
    moduleid,
    eventid,
    expand: formatExpand,
    willinrn: undefined,
  };
  const newInfoStr = JSON.stringify(newInfo);
  mobileTrace(newInfoStr);
};
/**
 * 公共的参数放到此处，具体业务，只传对应业务的参数
 * pageSource需要单独处理的是，非路由页面，比如弹框，modal，增加判断
 * 埋点组装的json字符串中，所有的key都是小写，无驼峰
 * 默认pageid 等传来来值都是一层，expand 按照要求传，因为如果是一层，没办法取出来，参数名字不确定
 */
// 点击方法
export const click = (info = {}) => {
  const infoStr = getInfoStr({info, pageIdMap}, 'click');
  addKeyValueAndTrace(infoStr);
};
// 曝光/浏览
export const page = (info = {}) => {
  const infoStr = getInfoStr({info, pageIdMap}, 'page');
  addKeyValueAndTrace(infoStr);
};
// 原始方法调用(埋点点击事件和曝光)
export const trace = (info?: string) => {
  const infoStr = getInfoStr({info, pageIdMap}, 'undefined');
  addKeyValueAndTrace(infoStr);
};
/**
 * leave时，传时间参数，
 * 2 对于弹框类的，当显示的一刻单独记录时间，消失的一刻，记录时间差，传过来值
 * 3 路由栈：A-B-C-D-E当路由发生变化时，就统计时间， 记录pre上个页面，栈中的最后一个为当前页面，
 *
 * 入栈时，停留时长计算：
 * A  --> AB， B 记录一个 startTime，算了一次A的时间差
 * 出栈时
 * AB --> A 算了一次A的startTime和B的时间差
 * 先记录一次 prePage， 作为from，   currentPage作为to
 * 调用时机：离开页面时或组件消失时调用, 时间可以作为参数传进来，也可以，直接认为A页面的离开，作为B页面的进入，
 * 始终保持停留页面只有一个
 *  */
export const leave = (info: {expand?: {}; pageid: string}) => {
  const currentTime = Date.now();
  const residencetime = `${currentTime - LogUtils.startTime}`;
  const expand = {
    residencetime,
  };
  // 单独处理duration时eleid的传值
  const eleid = getEleIdFromInfo(info);

  const newInfo = {
    ...info,
    eleid,
    expand: {
      ...expand,
      ...(info.expand || {}), // 优先传值进来的
    },
  };
  const infoStr = getInfoStr({info: newInfo, pageIdMap, durationMap}, 'leave');
  addKeyValueAndTrace(infoStr);
  // 当前页面的开始时间
  LogUtils.startTime = Date.now();
};

/**
 * @description: 根据pageid反查moduleid
 * @param {String} pageId
 * @param {Object} pageIdMap
 * @return {String} moduleId
 */
export const getModuleIdFromPageId = (pageId = '') => {
  let resultNumber = 0;
  switch (Number(pageId)) {
    case pageIdMap.panelHome:
    case pageIdMap.usageHistory:
    case pageIdMap.battery:
    case pageIdMap.updateAlert:
      resultNumber = moduleIdMap.panelHome;
      break;
    case pageIdMap.deviceMsg:
    case pageIdMap.detailListAlert:
    case pageIdMap.detailList:
    case pageIdMap.editName:
      resultNumber = moduleIdMap.detailList;
      break;
    default:
      break;
  }
  const result = `${resultNumber}`;
  return result;
};
/**
 * @description: duration时根据pageid获取eleid
 * @param {Object} info
 * @return {String}
 */
export const getEleIdFromInfo = (info: {pageid: string}) => {
  const {pageid = ''} = info;
  let resultNumber = eleIdMap.Home_Page_Stay_Duration;
  switch (Number(pageid)) {
    // 通用部分
    case pageIdMap.detailList:
      resultNumber = eleIdMap.Device_Details_Page_Stay_Duration;
      break;
    case pageIdMap.detailListAlert:
      resultNumber = eleIdMap.Remove_Device_Dialog_Box_Page_Stay_Duration;
      break;
    case pageIdMap.editName:
      resultNumber = eleIdMap.Modify_Equipment_Name_Page_Stay_Duration;
      break;
    case pageIdMap.deviceMsg:
      resultNumber = eleIdMap.Device_Information_Page_Stay_Duration;
      break;
    case pageIdMap.usageHistory:
      resultNumber = eleIdMap.Useage_History_Page_Stay_Duration;
      break;
    case pageIdMap.battery:
      resultNumber = eleIdMap.Battery_Information_Page_Stay_Duration;
      break;
    default:
      break;
  }

  const result = `${resultNumber}`;
  return result;
};

/**
 * @description: value字符串化处理，紧针对一层key-value
 * @param {Object} info
 * @return {Object}
 */
const getFormatExpand = (info: {expand: {[key: string]: string}}) => {
  // value如果为number，将其字符串化
  const originalExpand = info.expand || {};
  const result: {[keys: string]: string} = {};
  Object.keys(originalExpand).forEach(key => {
    const value = info?.expand[key];
    if (isNumber(value)) {
      result[key] = `${value}`;
    } else {
      result[key] = value;
    }
  });
  return Object.keys(result).length > 0 ? result : undefined;
};

const tracer = {
  click,
  page,
  trace,
  leave,
};
export {tracer};
export {pageIdMap};
export {eleIdMap};
export default tracer;
