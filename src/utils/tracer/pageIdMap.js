/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2023-02-09 11:35:31
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-09-11 10:35:14
 * @FilePath: /61004/src/utils/tracer/pageIdMap
 * @Description: 埋点的pageId映射列表
 * 命名规则和route 中去掉View之后，首字母小写，严格遵守
 */
import {PAGE_IDS} from './pageConstants';

// T R C对应不同的id
const commonMap = {
  detailList: PAGE_IDS.DETAIL_LIST, // 设备详情页面
  detailListAlert: PAGE_IDS.DETAIL_LIST_ALERT, // 移除设备对话框页面
  editName: PAGE_IDS.EDIT_NAME, // 修改设备名称对话框页面
  deviceMsg: PAGE_IDS.DEVICE_MSG, // 设备信息
};

// 3.18日 埋点文档错误较多，待泉峰产品修正给最新的，延迟1pd
const pageIdMap = {
  ...commonMap,
  // home页面及控件
  panelHome: PAGE_IDS.PANEL_HOME, // 首页
  usageHistory: PAGE_IDS.USAGE_HISTORY, // 使用历史
  battery: PAGE_IDS.BATTERY, // 电池页面
  updateAlert: PAGE_IDS.UPDATE_ALERT, // 升级弹框
};

export default pageIdMap;
