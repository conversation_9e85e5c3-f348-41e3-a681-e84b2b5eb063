/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2023-02-09 11:35:26
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-09-11 10:15:03
 * @FilePath: /61004/src/utils/tracer/commonEleIdMap
 * @Description: 面板通用eleid 映射表
 */
import {ELEMENT_IDS} from './constants';

const commonDetailList = {
  Return_Button_Click: ELEMENT_IDS.RETURN_BUTTON, // 返回按钮点击
  Equipment_Name_Button_Click: 2, // 设备名称按钮点击
  Common_Device_Registration_Button_Click: 3, // 设备注册按钮点击
  Common_Firmware_Update_Button_Click: 12, // 固件升级按钮点击
  Product_Encyclopedia_Button_Click: 4, // 产品百科按钮点击
  Equipent_Information_Button_Click: 5, // 设备信息按钮点击
  Enclosure_Button_Click: 6, // 附件按钮点击
  Share_Device_Click: 7, // 分享设备按钮点击
  FeedBack_Button_Click: 10, // 反馈按钮点击
  Device_Notification_Button_Click: 9, // 设备消息按钮点击

  // 和通用有小变动
  Delete_Device_Button_Click: 8, // 移除设备按钮点击
  Device_Details_Page_Stay_Duration: 11, // 设备详情页面停留时长
};
// 移除设备弹框
const commonDetailListAlert = {
  Confirm_Button_Click: ELEMENT_IDS.CONFIRM_BUTTON, // 确定按钮点击
  Detail_List_Alert_Cancel_Button_Click: 2, // 取消按钮点击
  Remove_Device_Success: 3,
  Remove_Device_Fail: 4,
  Remove_Device_Dialog_Box_Page_Stay_Duration:
    ELEMENT_IDS.REMOVE_DEVICE_DIALOG_BOX_STAY_DURATION, // 移除设备对话框页面停留时长
};
const commonEditName = {
  Edit_Name_Confirm_Button_Click: 1, // 保存按钮
  Edit_Name_Cancel_Button_Click: 2, // 相当于返回按钮
  Modify_Equipment_Name_Page_Stay_Duration: 2, // 确定按钮点击
};
const commonDeviceMsg = {
  Return_Button_Click: 1, // 返回按钮点击
  Device_Information_Page_Stay_Duration: 2, // 设备信息页面停留时长
  Battery_Information_Page_Stay_Duration: 0, // 电池信息页面停留时长
};

const commonUsageHistory = {
  Useage_History_Page_Stay_Duration: 2, // 使用历史页面停留时长
};
const common = {
  ...commonDetailList,
  ...commonDetailListAlert,
  ...commonEditName,
  ...commonDeviceMsg,
  ...commonUsageHistory,
};

export default common;
export {common};
