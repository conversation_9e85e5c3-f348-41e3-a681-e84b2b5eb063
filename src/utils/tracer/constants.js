/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2024-09-11 10:00:00
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-09-11 10:00:00
 * @FilePath: /61004/src/utils/tracer/constants
 * @Description: 埋点常量定义
 */

// 埋点元素ID常量
export const ELEMENT_IDS = {
  // 通用按钮ID
  RETURN_BUTTON: 1,
  CONFIRM_BUTTON: 1,

  // 设备详情相关
  DEVICE_DETAILS_BUTTON: 3,
  REMOTE_UNLOCK_BUTTON: 4,
  REMOTE_CONTROL_BUTTON: 5,
  DEVICE_LOCATION_BUTTON: 6,
  VIEW_BATTERY_BUTTON: 7,
  USAGE_DETAILS_BUTTON: 8,
  FAULT_MESSAGE_BUTTON: 6,
  FIRMWARE_UPDATE_BUTTON: 10,
  REGISTRATION_BUTTON: 11,
  ACCESSORY_BUTTON: 12,
  DEVICE_REGISTRATION_BUTTON: 21,
  HOME_PAGE_UNLOCK_ALERT_EXPOSURE: 5, // 首页解锁弹框曝光
  MOWING_PERFORMANCE_MODE_CLICK: 12, // 割草性能模式点击
  DAYTIME_LIGHT_MODE_CLICK: 13, // 日间照明模式点击
  LIGHTOFF_DELAY_SLIDE_CLICK: 14, // 延时关灯滑动点击
  BACK_UP_ALERT_SWITCH_CLICK: 15, // 备份警报开关点击
  USAGE_HISTORY_PAGE_CLICK: 16, // 使用历史页面点击

  // 页面停留时长
  HOME_PAGE_STAY_DURATION: 17,
  DEVICE_DETAILS_PAGE_STAY_DURATION: 11,
  REMOVE_DEVICE_DIALOG_BOX_STAY_DURATION: 5,
  USAGE_HISTORY_PAGE_STAY_DURATION: 2,
  VIEW_BATTERY_STAY_DURATION: 0,
};
