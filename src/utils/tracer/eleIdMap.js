/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2023-10-12 14:04:33
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-09-11 10:10:18
 * @FilePath: /61004/src/utils/tracer/eleIdMap
 * @Description: elid 映射表
 */
/**
 *  埋点的pageId映射列表
 *  单个map命名规则和route 中去掉View之后，首字母小写，严格遵守
 */
import common from './commonEleIdMap';
import {ELEMENT_IDS} from './constants';

const panelHome = {
  Return_Button_Click: ELEMENT_IDS.RETURN_BUTTON, // 点击返回按钮
  Device_Details_Button_Click: ELEMENT_IDS.DEVICE_DETAILS_BUTTON, // 点击设备详情按钮
  Device_Details_Button_Click_Exposure: ELEMENT_IDS.DEVICE_DETAILS_BUTTON, // 点击设备详情
  Remote_Unlock_Button_Click: ELEMENT_IDS.REMOTE_UNLOCK_BUTTON, // 点击远程解锁按钮
  View_Battery_Button_Click: ELEMENT_IDS.VIEW_BATTERY_BUTTON, // 点击查看电池按钮
  Remote_Control_Button_Click: ELEMENT_IDS.REMOTE_CONTROL_BUTTON, // 点击远程控制按钮
  Device_Location_Button_Click: ELEMENT_IDS.DEVICE_LOCATION_BUTTON, // 点击设备定位按钮
  RN_Device_Status_Button_Click: ELEMENT_IDS.VIEW_BATTERY_BUTTON, // 点击设备状态按钮
  Usage_Details_Button_Click: ELEMENT_IDS.USAGE_DETAILS_BUTTON, // 点击使用详情按钮
  Fault_Message_Button_Click: ELEMENT_IDS.FAULT_MESSAGE_BUTTON, // 点击故障信息按钮
  Firmware_Update_Button_Click: ELEMENT_IDS.FIRMWARE_UPDATE_BUTTON, // 点击固件更新按钮
  RN_Registration_Button_Click: ELEMENT_IDS.REGISTRATION_BUTTON, // 点击注册按钮
  RN_Accessory_Button_Click: ELEMENT_IDS.ACCESSORY_BUTTON, // 点击配件按钮
  Home_Page_Stay_Duration: ELEMENT_IDS.HOME_PAGE_STAY_DURATION, // 停留首页页面
  Device_Registration_Button_Click: ELEMENT_IDS.DEVICE_REGISTRATION_BUTTON, // 点击设备注册按钮
  Home_Page_Unlock_Alert_Exposure: ELEMENT_IDS.HOME_PAGE_UNLOCK_ALERT_EXPOSURE, // 点击远程解锁弹窗
  Mowing_Performance_Mode_Click: ELEMENT_IDS.MOWING_PERFORMANCE_MODE_CLICK, // 刀盘模式性能按钮
  Daytime_Light_Mode_Click: ELEMENT_IDS.DAYTIME_LIGHT_MODE_CLICK, // 日行灯模式切换按钮
  LightOff_Delay_Slide_Click: ELEMENT_IDS.LIGHTOFF_DELAY_SLIDE_CLICK, // 大灯延迟关闭按钮
  Back_Up_Alert_Switch_Click: ELEMENT_IDS.BACK_UP_ALERT_SWITCH_CLICK, // 倒车提醒开关按钮
  Usage_History_Page_Click: ELEMENT_IDS.USAGE_HISTORY_PAGE_CLICK, // 点击使用历史页面按钮
};

// 设备定位页面
const deviceLocation = {
  Device_Location_Stay_Duration: 0, // 设备定位页面停留时长
  Direction_Button_Click: 1, // 方向按钮点击
  Tracks_History_Button_Click: 2, // 轨迹历史按钮点击
};
const trackHistory = {
  Tracks_History_Stay_Duration: 0, // 轨迹历史页面停留时长
};
// 使用历史，具体id等产品确定
const usageHistory = {
  Return_Button_Click: 1, // 返回按钮点击
  Usage_History_Page_Stay_Duration: 2, // 使用历史页面停留时长
  Day_Button_Click: 3, // 日按钮点击
  Week_Button_Click: 4, // 周按钮点击
  Month_Button_Click: 5, // 月按钮点击
  Total_Time_Button_Click: 6, // 总时间按钮点击
  CO2_Reduction_Button_Click: 7, // CO2减排按钮点击
  Mowing_Area_Button_Click: 8, // 割草区域按钮点击
  Driving_Distance_Button_Click: 9, // 行驶距离按钮点击
  Power_Consumption_Button_Click: 10, // 能量消耗按钮点击
};
const battery = {
  View_Battery_Stay_Duration: 0, // 查看电池页面停留时长
  Return_Button_Click: 1, // 返回按钮点击
};
// 使用数据
const usageDetail = {
  Return_Button_Click: 1, // 返回按钮点击
  Usage_Details_Page_Stay_Duration: 2, // 使用详情页面停留时长
};

const detailList = {
  Return_Button_Click: 1, // 返回按钮点击
  Equipment_Name_Button_Click: 2, // 设备名称按钮点击
  Device_Registration_Button_Click: 11, // 设备注册按钮点击
  Firmware_Update_Button_Click: 9, // 固件更新按钮点击
  Product_Information_Button_Click: 4, // 产品百科按钮点击
  Equipent_Information_Button_Click: 5, // 设备信息按钮点击
  Accessories_Button_Click: 7, // 配件按钮点击
  Device_Notification_Button_Click: 9, // 设备消息按钮点击
  Delete_Device_Button_Click: 8, // 删除设备按钮点击
  Delete_Device_Alert_Stay_Duration: 5, // 删除设备对话框停留时间
  Device_Details_Page_Stay_Duration: 11, // 设备详情页面停留时长
  Device_Status_Button_Click: 10, // 设备状态按钮点击
  Change_Passcode_Button_Click: 12, // 更改密码按钮点击
  Unbind_Device_Confirm_Click: 1, // 解绑设备确认按钮
};
const changePassword = {
  Change_Passcode_Save_Button_Click: 0, // 保存按钮点击
  Change_Passcode_Return_Button_Click: 1, // 返回按钮点击
  Reset_Password_Button_Click: 2, // 重置密码按钮点击
  Reset_Password_Yes_Button_Click: 3, // 确认按钮点击
  Reset_Password_No_Button_Click: 4, // 取消按钮点击
};
const control = {
  Remote_Control_Stay_Duration: 0, // 远程控制页面停留时长
};
const updateAlert = {
  Update_App_Window_Update_Button_Click: 0, // 升级按钮确定
  Update_App_Window_Later_Button_Click: 1, // 升级按钮取消
};

// 注意相同的key：Return_Button_Click 对应的值都是 1,所以覆盖后无影响
const eleIdMap = {
  ...common,
  ...panelHome,
  ...usageDetail,
  ...detailList,
  ...deviceLocation,
  ...usageHistory,
  ...changePassword,
  ...control,
  ...battery,
  ...trackHistory,
  ...updateAlert,
};

export default eleIdMap;
