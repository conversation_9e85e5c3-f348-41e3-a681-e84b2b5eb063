/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2024-02-20 17:59:05
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-07-19 17:08:07
 * @FilePath: /61004/src/utils/tracer/durationMap
 * @Description: 停留时间
 */
/**
 * 以下为路由页面设置eleid,key为pageid，value为eleid，
 * 非路由页面需要单独处理
 */

import eleIdMap from './eleIdMap';
import {PAGE_IDS} from './pageConstants';
import {ELEMENT_IDS} from './constants';

const durationMap = {
  [PAGE_IDS.DETAIL_LIST]: ELEMENT_IDS.DEVICE_DETAILS_PAGE_STAY_DURATION,
  [PAGE_IDS.DETAIL_LIST_ALERT]:
    ELEMENT_IDS.REMOVE_DEVICE_DIALOG_BOX_STAY_DURATION,
  [PAGE_IDS.EDIT_NAME]: eleIdMap.Modify_Equipment_Name_Page_Stay_Duration,
  [PAGE_IDS.DEVICE_MSG]: eleIdMap.Device_Information_Page_Stay_Duration,

  [PAGE_IDS.PANEL_HOME]: ELEMENT_IDS.HOME_PAGE_STAY_DURATION,
  [PAGE_IDS.USAGE_HISTORY]: ELEMENT_IDS.USAGE_HISTORY_PAGE_STAY_DURATION,
  [PAGE_IDS.BATTERY]: ELEMENT_IDS.VIEW_BATTERY_STAY_DURATION,
};

export default durationMap;
