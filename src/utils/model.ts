/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2023-09-25 17:44:24
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-07-19 15:25:51
 * @FilePath: /61004/src/utils/model
 * @Description: 物模型定义
 * 物模型定义wiki: http://wiki.chervon.com.cn/pages/viewpage.action?pageId=22822533
 */
import {Utils} from 'cvn-panel-kit';

const {NumberUtils} = Utils;

// DCDC模块状态枚举
export const dcdcStateEnum = {
  retainType: 0, // 保留
  charging: 1, // 充电
  chargeFull: 3, // 充满
  chargeWait: 5, // 充电等待
  overTemp: 6, // 过温
  chargeError: 7, // 充电故障
  empty: 8, // 空仓
};

// 16进制属性 部分电池相关属性
export const hexPropertyMap = {
  // ztr2.0 电池仓电量
  ztr_battery_level: '040F', // uint8
  // ztr2.0 电池仓状态
  ztr_battery_status: '13AA', // uint8
  // DC-DC仓位电量,255表示空仓
  dc_dc_storage_battery: '07D8', // uint8
  // ZTR电池仓1电量
  ztr_battery_storage1: '1393', // raw
  // ZTR电池仓2电量
  ztr_battery_storage2: '1394', // raw
  // ZTR电池仓3电量
  ztr_battery_storage3: '1395', // raw
  // ZTR电池仓4电量
  ztr_battery_storage4: '1396', // raw
  // ZTR电池仓1故障状态
  ztr_battery_storage1_fault: '139D', // raw
  // ZTR电池仓2故障状态
  ztr_battery_storage2_fault: '139E', // raw
  // ZTR电池仓3故障状态
  ztr_battery_storage3_fault: '139F', // raw
  // ZTR电池仓4故障状态
  ztr_battery_storage4_fault: '13A0', // raw
  // DC-DC电池仓充电状态/空仓等信息
  battery_charge_status: '13A7', // enum
  // ZTR DC-DC剩余充满时间，分钟
  ztr_dc_remaining_full_time: '13A8', // int
  // 修改密码
  modify_password: '5DC4', // string
};

// 属性
export const propertyMap = {
  // ota 是否可升级 0: 允许升级，非0：不允许升级
  ota_isupdate: '1000', // int
  // 剩余电量百分比,0-100
  battery_percentage: '1011', // int
  // 总割草面积,单位平方分米
  total_mown_area: '1014', // int
  // 总工作时长,单位秒
  // 总行驶时长由 1016改为1068
  total_working_time: '1068', // int
  // 剩余割草时长，秒
  remaining_mowing_time: '1021', // int
  // 单次工作时长
  single_working_time: '1031', // int
  // 单次功耗
  single_power_consumption: '1025',
  // 单次割草面积
  single_mown_area: '1035', // int
  // GPS坐标
  gps_coordinate: '1036', // string
  // 数组表示6块电池 0-100：表示每个仓位剩余电量百分比 FF: 空仓
  battery_level: '1039', // string
  // 总行驶距离
  total_driving_distance: '1068',
  // 总割草时长
  single_moving_time: '4001',
  // 电池故障状态：数组6 / 0：正常（或未插包）1：过温 2：故障
  battery_status: '5034', // string

  // 整车充满剩余时间，分钟，3.1日
  full_vehicle_remaining_time: '2001', // int
  // DC-DC仓位电量,255表示空仓
  dc_dc_storage_battery: '2008', // uint8
  // 当前行驶速度,单位mph，  7.19日改成 cm/s
  current_driving_speed: '5001', // int
  // 刀盘转速,单位转/分钟,  >0 表示转动，=0 表示停止
  blade_rotation_speed: '5002', // int
  // 刀片模式
  mowing_performance_mode: '24006',
  // 倒车报警灯
  back_up_alert_mode: '24007',
  // 日光灯
  day_time_light_mode: '24008',
  // 延时关灯
  light_off_delay: '24009',
  // 座椅是否有人
  seat_has_people: '5003', // bool
  /**
   * ZTR工作状态
   * 0 - 空闲、1 - 自走(主动驾驶)、2 - 割草、3 - 充电、4 - 运输(被其他工具载运)
   */
  ztr_work_status: '5004', // enum
  // 翻车告警
  overturn_alarm: '5005', // bool
  // 平均行驶速度 cm/s
  average_driving_speed: '5006', // int
  // 单次行驶距离
  single_driving_distance: '5008', // int
  // ZTR电池仓1电量
  ztr_battery_storage1: '5011', // raw
  // ZTR电池仓2电量
  ztr_battery_storage2: '5012', // raw
  // ZTR电池仓3电量
  ztr_battery_storage3: '5013', // raw
  // ZTR电池仓4电量
  ztr_battery_storage4: '5014', // raw
  // ZTR电池仓1故障状态
  ztr_battery_storage1_fault: '5021', // raw
  // ZTR电池仓2故障状态
  ztr_battery_storage2_fault: '5022', // raw
  // ZTR电池仓3故障状态
  ztr_battery_storage3_fault: '5023', // raw
  // ZTR电池仓4故障状态
  ztr_battery_storage4_fault: '5024', // raw
  // 整机电池故障状态： 0：正常（或未插包）1：过温 2：故障
  all_battery_status: '5033',
  // DC-DC电池仓充电状态
  /**
   * 电池充电状态
    4.16日修改，描述更新了
    0：保留
    1：充电中
    3：充满
    5：充电等待中（EMS不允许充电）
    6：过温
    7：充电故障
    8：空仓
   */
  battery_charge_status: '5031', // enum
  // ZTR DC-DC剩余充满时间，分钟
  ztr_dc_remaining_full_time: '5032', // int
};

// 服务
export const serviceMap = {
  // uint16
  remote_control_angle: '24001',
  // 远程锁定,	0：物理钥匙插入 1：未插入钥匙app未解锁 2：未插入钥匙app解锁
  remote_lock: '24010',
  // 修改密码 string
  modify_password: '24004',
  // 坡道告警 enum,(3.1新增需求)
  /**
   * enum
   * 0：15°、1：20°、2：25°
   * 默认值：15°
   */
  slope_alarm: '24005',
  // 时区
  time_zone: '20006', // int
  /**
   * @description 重置密码动作
   * false - 已经设置过密码
   * true - 未设置过密码
   * 默认初始密码，认为是重置过，初始值上报true；
   * 当收到修改密码指令后上报false；
   * 当收到该值为true时，返回true，且将密码重置为默认密码，并上报一次默认密码
   */
  reset_pwd: '240000006',
  light_delayTime: '61',
  reverse_reminder_setting: '62',
};

// 事件
const eventMap = {
  // 190000：“无故障”
  fault_messages: '44005',
  // event
  working_status_message: '81',
  error_status_message: '85',
};

export const map = {
  ...propertyMap,
  ...serviceMap,
  ...eventMap,
};

export const dpTypeMap = {
  rawKey: '00', // array 判断和这个一样
  boolKey: '01',
  valueKey: '02', // int， 物模型中的设置值，unit8、unit16等统一按照该类型处理，可改变对应字节长度
  stringKey: '03',
  enumKey: '04',
  paramKey: '05',
  unit8IntKey: '06',
  uint16IntKey: '07',
  arrayKey: '08',
  structKey: '09',
};

export const dp_len_1 = NumberUtils.numToHexString(1, 4).toUpperCase();
export const dp_len_2 = NumberUtils.numToHexString(2, 4).toUpperCase();
export const dp_len_15 = NumberUtils.numToHexString(15, 4).toUpperCase();
export const dp_len_84 = NumberUtils.numToHexString(84, 4).toUpperCase();
export const dp_len_4 = NumberUtils.numToHexString(4, 4).toUpperCase();
export const dp_len_5 = NumberUtils.numToHexString(5, 4).toUpperCase();

export default map;
