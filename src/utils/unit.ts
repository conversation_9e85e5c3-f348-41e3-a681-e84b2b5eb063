/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2024-06-26 11:06:12
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-07-02 19:40:31
 * @FilePath: /61004/src/utils/unit
 * @Description: 单位换算相关
 */
/**
 * @description: mph --> km/h
 * @param {Number} mph
 * @returns km/h
 */
export const mphToKmh = (mph = 0) => {
  return mph * 1.60934;
};
/**
 * @description: mph --> km/h
 * @param {Number} mph
 * @returns km/h
 */
export const mToKm = (m = 0) => {
  return m / 1000;
};
/**
 * @description: m --> miles
 * @param {Number} mph
 * @returns km/h
 */
export const mToMiles = (m = 0) => {
  return m / 1609.34;
};
