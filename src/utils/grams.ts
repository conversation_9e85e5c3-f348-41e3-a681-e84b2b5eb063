/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2023-10-12 14:04:33
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-06-13 16:33:36
 * @FilePath: /61004/src/utils/grams
 * @Description: 工具类，
 */
/**
 * @description: 克--> 千克
 * @param {Number} grams 克
 * @return {Number} 千克
 */
export const gramsToKilograms = (grams: number): number => {
  const kilograms = grams / 1000;
  return Number(kilograms.toFixed(3));
};
/**
 * @description: 克--> 英镑
 * @param {Number} grams 克
 * @return {Number} 英镑
 */
export const gramsToPounds = (grams: number): number => {
  const pounds = grams / 453.59237;
  return Number(pounds.toFixed(3));
};
