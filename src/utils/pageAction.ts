import {mobile} from 'cvn-panel-kit';
import {routeProps} from 'cvn-panel-kit/src/utils';
let lastPressTime = 0;
const waitTime = 500; // 点击间隔时间
/**
 * @param {object} navigation
 * @param {string} pageName
 * @param {Record<string>} params
 * @returns {void}
 */
export const goPage = (
  navigation: {[key: string]: Function},
  pageName: string,
  params = {},
) => {
  const currentTime = Date.now();
  if (!pageName) {
    return;
  }
  if (currentTime - lastPressTime < waitTime) {
    return;
  }
  lastPressTime = currentTime;
  navigation.push(pageName, params);
};
/**
 * @param {object} navigation
 * 页面返回
 * @param { string } key 返回指定页面对应的key
 * @returns {void}
 */
export const goBack = (
  navigation: {[key: string]: Function},
  key: string = '',
): void => {
  const currentTime = Date.now();
  if (currentTime - lastPressTime < waitTime) {
    return;
  }
  lastPressTime = currentTime;
  const canBack = navigation.canGoBack();
  if (!canBack) {
    mobile.back();
  } else if (key.length > 0) {
    navigation.navigate(key);
  } else {
    navigation.goBack();
  }
};
/**
 * @description: 安卓物理返回键处理
 * @param {object} navigation
 * @returns {void}
 */

export const androidGoBack = (navigation: {[key: string]: Function}) => {
  if (routeProps.routes?.length > 1) {
    navigation.goBack();
  } else {
    mobile.back();
  }
};
