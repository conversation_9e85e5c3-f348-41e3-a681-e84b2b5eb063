/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2023-10-12 14:04:33
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-07-02 16:34:14
 * @FilePath: /61004/src/utils/range
 * @Description: 工具类
 */
import dayjs from 'dayjs';
/**
 * @description: 19:30 -> 时间戳秒级：1676547
 * @param {String} timeStr
 * @return {Number} 秒级时间戳
 */
export const timeStr2Stamp = (timeStr: string): number => {
  const dateStr = dayjs().format('YYYY/MM/DD'); // '2019/01/25'
  const dateTimeStr = `${dateStr} ${timeStr}`;
  // valueOf release不行，用unix
  const result = dayjs(dateTimeStr).unix();
  return result;
};
/**
 * @description: 秒级时间戳转成 12进行时间字符串
 * @example: type=12hours: 1701945022 ---> 6:30 PM;  type=24hours: 1701945022 -> 18:30
 * @param {Number} timeStamp
 * @param {String} type 原生传的字段，欧洲:'24hours'，北美:'12hours'
 * @return {String} 12进行时间
 */
export const stamp2TimeStr = (
  timeStamp: number = 0,
  type: string = '12hours',
): string => {
  let result = dayjs(timeStamp * 1000).format('HH:mm');
  if (type === '12hours') {
    const array = result.split(':');
    const hour = array.length > 0 ? array[0] : '00';
    const minute = array.length > 1 ? array[1] : '00';
    result = convertToAMPM(Number(`${hour}${minute}`));
  }
  return result;
};
/**
 * @description: 首页根据时间戳获取刻度
 * @param {Number} timeStamp 毫秒级
 * @return {Number} index
 */
export const getIndexFromTimeStamp = (timeStamp: number = 0): number => {
  const result = getIndex(timeStamp);
  return result;
};
/**
 * @description: 当前时间下一个15分钟整数倍
 * @return {Number} index
 */
export const getNextQuarterIndexFromTimeStamp = (): number => {
  const timeStamp = dayjs().valueOf();
  const result = getIndex(timeStamp);
  return result;
};
/**
 * @description: 获取index封装
 * @param {Number} timeStamp
 * @return {Number} index
 */
export const getIndex = (timeStamp: number = 0): number => {
  const currentHour = dayjs(timeStamp).hour();
  const currentMinute = dayjs(timeStamp).minute();
  const minutes = currentHour * 60 + currentMinute;
  const result = Math.floor(minutes);
  return result;
};

/**
 * @description: 24小时 时分(四位数字  2323) 转成 12小时进制
 * @example 2323  --> 11:23 AM
 * @param {Number} time24
 * @return {String}
 */
export const convertToAMPM = (time24: number): string => {
  if (time24 < 0 || time24 > 2399) {
    return '无效的时间';
  }

  let hours = Math.floor(time24 / 100);
  const minutes = time24 % 100;
  let ampm = 'AM';

  if (hours >= 12) {
    ampm = 'PM';
    if (hours >= 12) {
      hours -= 12;
    }
  }
  const formattedTime = `${hours}:${minutes
    .toString()
    .padStart(2, '0')} ${ampm}`;
  return formattedTime;
};
/**
 * @description: 对象 --> 时间戳(秒级)
 * @return {Object} info
 */
export const getTimeStampWithInfo = (info: {
  hours: number;
  minutes: number;
}): number => {
  const {hours, minutes} = info;
  const timeStr = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(
    2,
    '0',
  )}`;
  const result = timeStr2Stamp(timeStr);
  return result;
};
/**
 * @description starts from 显示
 * @param {Object} info
 * @returns
 */
export const getAMPMResult = (info: {hours: 0; minutes: 0}) => {
  const {hours = 0, minutes = 0} = info;
  const timeStr = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(
    2,
    '0',
  )}`;
  const newTimeStr = timeStr.split(':').join('');
  const result = convertToAMPM(Number(newTimeStr));
  return result;
};
