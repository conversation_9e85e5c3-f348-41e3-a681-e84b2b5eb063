/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2023-02-09 11:35:31
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-07-02 19:39:36
 * @FilePath: /61004/src/utils/topic
 * @Description: topic定义
 */
const topicMap = {
  connectWIFI: '$aws/events/presence/connected/',
  disConnectWIFI: '$aws/events/presence/disconnected/',
  preFix: '$aws/things/',
  // 物模型查询
  shadowGet: '/shadow/get',
  // 物模型topic
  shadowUpdateAcceptedSuffix: '/shadow/update/accepted',
  // 物模型 单次拉取
  shadowGetAcceptedSuffix: '/shadow/get/accepted',
  // 上报属性
  shadowUpdateSuffix: '/shadow/update',

  preFixWithoutSymbol: 'aws/things/', // 少个s  9.8日下午，丽娜提交代码，需要马力下载后，改为things
  // ota check
  otaSuffix: '/ota/check',
  // ota 接收
  otaAcceptedSuffix: '/ota/check/accepted',
};
export default topicMap;
