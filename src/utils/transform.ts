/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2024-02-23 10:25:41
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-07-19 14:14:45
 * @FilePath: /61004/src/utils/transform
 * @Description: 转换工具
 */
/**
 * @description 平方米--> acre
 * 1平方米等于0.000247 acre
 * @param {Number} squareMeter 平方米
 * @returns String
 */
export const squareMeterToAcreStr = (squareMeter: number) => {
  const acre = squareMeter * 0.000247;
  return acre.toFixed(3);
};
/**
 * @description 平方米--> acre
 * 1平方米等于0.000247 acre
 * @param {Number} squareMeter 平方米
 * @returns Number
 */
export const squareMeterToAcre = (squareMeter: number) => {
  const acre = squareMeter * 0.000247;
  return acre;
};

/**
 * @description Converts meters to miles
 * @param {Number}} meter - The value in meters to be converted
 * @returns The converted value in miles
 */
export const convertMeterToMiles = (meter: number) => {
  const result = meter / 1609.344; // 1英里 = 1609.344米
  return result;
};
/**
 * @description cm/s 转换成 mph
 * @param {*} cmPerSecond
 * @returns mph
 */
export const cmPerSecondToMph = (cmPerSecond = 0) => {
  const mph = cmPerSecond * 0.0223694;
  return mph;
};

export const mathRoundToFixed = (value: string | number, num: number) => {
  const numberValue = Number(value);
  const multi = Math.pow(10, num);
  const multiResult = Math.round(numberValue * multi) / multi;
  return multiResult.toFixed(num);
};
