import {OptionalValue, BooleanOrUndefined} from './common';

declare global {
  // eslint-disable-next-line no-var
  var loadingTimer: OptionalValue<NodeJS.Timeout>;
  // eslint-disable-next-line no-var
  var hasLoading: BooleanOrUndefined;

  // React Native ErrorUtils
  // eslint-disable-next-line no-var
  var ErrorUtils: {
    setGlobalHandler: (
      handler: (error: Error, isFatal?: boolean) => void,
    ) => void;
    getGlobalHandler: () => OptionalValue<
      (error: Error, isFatal?: boolean) => void
    >;
  };

  // Development flag
  // eslint-disable-next-line no-var
  var __DEV__: boolean;
}

export {};
