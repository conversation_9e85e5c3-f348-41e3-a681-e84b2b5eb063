/**
 * 设备相关类型定义
 */

/**
 * 设备状态枚举
 */
export enum DeviceStatus {
  /** 空闲状态 */
  IDLE = 0,
  /** 工作状态 */
  WORKING = 1,
  /** 充电状态 */
  CHARGING = 2,
  /** 错误状态 */
  ERROR = 3,
}

/**
 * 设备工作状态枚举（扩展）
 */
export enum DeviceWorkStatus {
  /** 空闲 */
  IDLE = 0,
  /** 自走 */
  SELF_DRIVING = 1,
  /** 割草 */
  MOWING = 2,
  /** 充电 */
  CHARGING = 3,
  /** 移动 */
  MOVING = 4,
}

/**
 * 设备基本信息接口
 */
export interface DeviceInfo {
  /** 设备ID */
  deviceId: string;
  /** 设备名称 */
  deviceName: string;
  /** MAC地址 */
  mac: string;
  /** 产品ID */
  productId: string;
  /** 设备状态 */
  status: DeviceStatus;
  /** 电池电量百分比 */
  batteryLevel: number;
  /** 是否在线 */
  isOnline: boolean;
  /** 最后更新时间 */
  lastUpdateTime?: number;
  /** 固件版本 */
  firmwareVersion?: string;
}

/**
 * 设备连接信息
 */
export interface DeviceConnectionInfo {
  /** 是否连接 */
  isConnected: boolean;
  /** 连接类型 */
  connectionType: 'wifi' | 'bluetooth' | 'offline';
  /** 信号强度 */
  signalStrength?: number;
  /** 连接时间戳 */
  connectedAt?: number;
}

/**
 * 设备位置信息
 */
export interface DeviceLocation {
  /** 纬度 */
  latitude: number;
  /** 经度 */
  longitude: number;
  /** 精度 */
  accuracy?: number;
  /** 时间戳 */
  timestamp: number;
}

/**
 * 设备配置信息
 */
export interface DeviceConfig {
  /** 设备ID */
  deviceId: string;
  /** 工作模式 */
  workMode: string;
  /** 自动充电 */
  autoCharge: boolean;
  /** 工作时间配置 */
  workSchedule?: WorkSchedule[];
}

/**
 * 工作时间配置
 */
export interface WorkSchedule {
  /** 星期几 (0-6, 0为周日) */
  dayOfWeek: number;
  /** 开始时间 (HH:mm) */
  startTime: string;
  /** 结束时间 (HH:mm) */
  endTime: string;
  /** 是否启用 */
  enabled: boolean;
}

// 初始化参数接口
export interface InitialParamsProps {
  deviceId: string; // 设备ID
  mac: string; // MAC地址
  deviceName: string; // 设备名称
  appSettingOfHour: number; // 北美12小时，欧洲24小时
  region: string; // 地区 NA/EU  EU:欧洲 US:北美 CN:中国
  timezone: string; // 时区
  productId: string;
  deviceDetail: string;
  appSettingOfUnit: string; // 温度单位
}

// 设备详情属性接口
export interface DeviceDetailProps {
  shareType: number; // 共享类型
  infoStatus: number; // 设备信息状态
  nickName: string; // 设备昵称
  deviceName: string; // 设备名称
  productId: string; // 产品ID
  commodityModel: string; // 商品型号
  sn: string; // 序列号
  deviceIcon: string;
  isOnline: number; // 是否在线
  version: string; // 版本号
}
