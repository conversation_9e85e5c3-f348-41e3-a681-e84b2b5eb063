/**
 * API相关类型定义
 */

/**
 * 通用API响应接口
 */
export interface ApiResponse<T = unknown> {
  /** 响应数据 */
  data: T;
  /** 状态码 */
  status: number;
  /** 响应消息 */
  message: string;
  /** 时间戳 */
  timestamp: number;
  /** 请求ID */
  requestId?: string;
}

/**
 * API错误接口
 */
export interface ApiError {
  /** 错误代码 */
  code: string;
  /** 错误消息 */
  message: string;
  /** 错误详情 */
  details?: unknown;
  /** 时间戳 */
  timestamp?: number;
  /** 请求ID */
  requestId?: string;
}

/**
 * 分页请求参数
 */
export interface PaginationParams {
  /** 页码 (从1开始) */
  page: number;
  /** 每页大小 */
  pageSize: number;
  /** 排序字段 */
  sortBy?: string;
  /** 排序方向 */
  sortOrder?: 'asc' | 'desc';
}

/**
 * 分页响应数据
 */
export interface PaginatedResponse<T> {
  /** 数据列表 */
  items: T[];
  /** 总数量 */
  total: number;
  /** 当前页码 */
  page: number;
  /** 每页大小 */
  pageSize: number;
  /** 总页数 */
  totalPages: number;
  /** 是否有下一页 */
  hasNext: boolean;
  /** 是否有上一页 */
  hasPrev: boolean;
}

/**
 * HTTP状态码枚举
 */
export enum HttpStatusCode {
  /** 成功 */
  OK = 200,
  /** 已创建 */
  CREATED = 201,
  /** 无内容 */
  NO_CONTENT = 204,
  /** 错误请求 */
  BAD_REQUEST = 400,
  /** 未授权 */
  UNAUTHORIZED = 401,
  /** 禁止访问 */
  FORBIDDEN = 403,
  /** 未找到 */
  NOT_FOUND = 404,
  /** 方法不允许 */
  METHOD_NOT_ALLOWED = 405,
  /** 请求超时 */
  REQUEST_TIMEOUT = 408,
  /** 冲突 */
  CONFLICT = 409,
  /** 内部服务器错误 */
  INTERNAL_SERVER_ERROR = 500,
  /** 服务不可用 */
  SERVICE_UNAVAILABLE = 503,
}

/**
 * API请求配置
 */
export interface ApiRequestConfig {
  /** 请求URL */
  url: string;
  /** 请求方法 */
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  /** 请求头 */
  headers?: Record<string, string>;
  /** 请求参数 */
  params?: Record<string, unknown>;
  /** 请求体 */
  data?: unknown;
  /** 超时时间 (毫秒) */
  timeout?: number;
  /** 是否需要认证 */
  requireAuth?: boolean;
}

/**
 * 文件上传响应
 */
export interface FileUploadResponse {
  /** 文件ID */
  fileId: string;
  /** 文件名 */
  fileName: string;
  /** 文件大小 */
  fileSize: number;
  /** 文件URL */
  fileUrl: string;
  /** 上传时间 */
  uploadTime: number;
}

/**
 * 批量操作请求
 */
export interface BatchOperationRequest<T> {
  /** 操作类型 */
  operation: 'create' | 'update' | 'delete';
  /** 数据列表 */
  items: T[];
}

/**
 * 批量操作响应
 */
export interface BatchOperationResponse {
  /** 成功数量 */
  successCount: number;
  /** 失败数量 */
  failureCount: number;
  /** 失败详情 */
  failures?: Array<{
    index: number;
    error: ApiError;
  }>;
}
