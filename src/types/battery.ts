/**
 * 电池相关类型定义
 */

import {StringOrUndefined} from './common';

/**
 * 电池状态枚举
 */
export enum BatteryStatus {
  /** 正常状态 */
  NORMAL = 0,
  /** 过热状态 */
  HOT = 1,
  /** 错误状态 */
  ERROR = 2,
  /** 组合错误状态 */
  COMBINED_ERROR = 3,
  /** 空电池状态 */
  EMPTY = 255,
}

/**
 * 充电状态枚举
 */
export enum ChargeStatus {
  /** 保留状态 */
  RESERVED = 0,
  /** 充电中 */
  CHARGING = 1,
  /** 充满 */
  FULL = 3,
  /** 充电等待中（EMS不允许充电） */
  WAITING = 5,
  /** 过温 */
  OVER_TEMP = 6,
  /** 充电故障 */
  FAULT = 7,
  /** 空仓 */
  EMPTY = 8,
}

/**
 * 电池信息接口
 */
export interface BatteryInfo {
  /** 电量百分比 (0-100) */
  level: number;
  /** 是否正在充电 */
  isCharging: boolean;
  /** 温度 (摄氏度) */
  temperature: number;
  /** 电压 (伏特) */
  voltage: number;
  /** 电池状态 */
  status: BatteryStatus;
  /** 充电状态 */
  chargeStatus?: ChargeStatus;
  /** 剩余充电时间 (分钟) */
  remainingChargeTime?: number;
  /** 预计使用时间 (分钟) */
  estimatedUsageTime?: number;
}

/**
 * 电池详情信息
 */
export interface BatteryDetailInfo {
  /** 是否存在电池 */
  didHave: boolean;
  /** 电池电量值 */
  value: number;
  /** 是否有错误 */
  isError: boolean;
  /** 是否过温 */
  overTemp: boolean;
}

/**
 * 电池仓信息
 */
export interface BatteryCompartmentInfo {
  /** 电池仓索引 */
  index: number;
  /** 电池详情 */
  battery: BatteryDetailInfo;
  /** 是否为空仓 */
  isEmpty: boolean;
}

/**
 * 电池管理系统状态
 */
export interface BatteryManagementStatus {
  /** 总电量百分比 */
  totalBatteryPercentage: number;
  /** 电池详情列表 */
  batteryDetails: BatteryDetailInfo[];
  /** DC-DC是否在充电 */
  isDCCharging: boolean;
  /** DC-DC充电剩余时间 */
  dcChargeRemainTime: number;
  /** 整车充电信息 */
  totalChargeInfo: TotalChargeInfo;
}

/**
 * 整车充电信息
 */
export interface TotalChargeInfo {
  /** 剩余充电时间（小时） */
  totalRemainTimeHour: StringOrUndefined;
  /** 剩余充电时间（分钟） */
  totalRemainTimeMinute: string;
  /** 是否正在充电 */
  isTotalCharging: boolean;
}

/**
 * 电池错误状态类型
 */
export type BatteryErrorStatus = 'normal' | 'hot' | 'error';

/**
 * 电池常量定义
 */
export const BATTERY_CONSTANTS = {
  /** 空电池值 */
  EMPTY_BATTERY_VALUE: 255,
  /** 最大电池百分比 */
  MAX_BATTERY_PERCENTAGE: 100,
  /** 最小电池百分比 */
  MIN_BATTERY_PERCENTAGE: 0,
  /** 所需电池数量 */
  REQUIRED_BATTERY_COUNT: 6,
} as const;
