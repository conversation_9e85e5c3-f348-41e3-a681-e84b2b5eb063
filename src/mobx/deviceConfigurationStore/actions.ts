import {computed} from 'mobx';
import DeviceConfigurationAndControlState from './state';
import ModelMap from '@utils/model';
import {RootStore} from '../rootStore';
import DeviceConnectionStore from '../deviceConnectionStore';
import logger from 'IOTRN/src/utils/logger';

// 延时关灯枚举 0：0秒 1：5秒 2：15秒 3：30秒 4：100秒 5：300秒
const lightOffDelayEnum: {[key: number]: number} = {
  0: 0,
  1: 60,
  2: 120,
  3: 180,
  4: 240,
  5: 300,
};
export default class DeviceConfigurationAndControlActions {
  state: DeviceConfigurationAndControlState;
  deviceConnectionStore: DeviceConnectionStore;
  constructor(state: DeviceConfigurationAndControlState, rootStore: RootStore) {
    this.state = state;
    this.deviceConnectionStore = rootStore.deviceConnectionStore;
  }

  // 获取OTA的升级状态
  getOTAUpdateStatus(res: {[x: string]: number}) {
    const {ota_isupdate} = ModelMap;
    const otaStatus = res[ota_isupdate];
    if (otaStatus === 0) {
      return true;
    }
    return false;
  }

  /**
   * @description: 升级文案
   * @param {String} value
   */
  setUpdateText = (value: string) => {
    this.state.updateText = value;
  };

  /**
   * @description: 设置 强制升级
   * @param {Boolean} value
   */
  setIsForceUpdate = (value: boolean) => {
    this.state.isForceUpdate = value;
  };
  /**
   * @description: 设置 最新ota 版本号
   * @param {Boolean} value
   */
  setNewOtaVersion = (value: string) => {
    this.state.newOtaVersion = value;
  };

  // 锁定状态
  @computed get lockStatus() {
    const result = Number(
      this.deviceConnectionStore.state.resWithNew[ModelMap.remote_lock],
    );
    return result;
  }

  // 设置刀片模式
  @computed get mowingPerformanceMode() {
    const {mowing_performance_mode} = ModelMap;
    const result =
      this.deviceConnectionStore.state.resWithNew[mowing_performance_mode];
    if (result) {
      return Boolean(Number(result));
    }
    return false;
  }

  // 设置报警灯开关
  @computed get backUpAlertModeValue() {
    const {back_up_alert_mode} = ModelMap;
    const result =
      this.deviceConnectionStore.state.resWithNew[back_up_alert_mode];
    if (result) {
      return Boolean(Number(result));
    }
    return false;
  }

  // 设置日光灯模式
  @computed get dayTimeLightValue() {
    const {day_time_light_mode} = ModelMap;
    const result =
      this.deviceConnectionStore.state.resWithNew[day_time_light_mode];
    return result;
  }

  // 设置延时关灯时间
  @computed get lightOffDelayValue() {
    const {light_off_delay} = ModelMap;
    const result =
      this.deviceConnectionStore.state.resWithNew[light_off_delay] ?? '0';
    return lightOffDelayEnum[result as number];
  }

  /**
   * @description: 设置刀盘最新状态
   * @param {Boolean} value
   */
  setBladeIsOpen = (value: boolean) => {
    this.state.bladeIsOpen = value;
  };

  /**
   * @description: 设置 ota升级的总成版本号
   * @param {String} customVersion 总成版本号
   */
  setCustomVersion = (customVersion: string) => {
    this.state.customVersion = customVersion;
  };

  /**
   * @description: 小红点处理，首页和仪表盘共用数据源
   * @param {Boolean} showRed
   */
  setShowRed = (showRed: boolean) => {
    this.state.showRed = showRed;
  };
}
