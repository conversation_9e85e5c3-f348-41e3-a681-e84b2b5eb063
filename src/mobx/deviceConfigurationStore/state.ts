export default class DeviceConfigurationAndControlState {
  // res: 0:OFF  1:ON
  backUpAlertMode = 0;
  // res: 0:OFF  1:RUNNING 2:ON
  dayTimeLight = 0;
  // enum: 	0：0秒  1：5秒  2：15秒 3：30秒 4：100秒  5：300秒
  lightOffDelay: number = 0;

  // res: true:Hyper  false:Standard
  movingPerformance: boolean = false;

  // 升级title，默认空串
  updateText: string = '';
  // 是否强制升级
  isForceUpdate: boolean = false;
  // ota 升级
  showRed: boolean = false;

  // 记录刀盘开关最新的状态
  bladeIsOpen: boolean = false;
  customVersion: string = '';
  newOtaVersion: string = '';
}
