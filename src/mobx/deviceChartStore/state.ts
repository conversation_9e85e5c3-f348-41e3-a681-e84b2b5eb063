import Strings from 'IOTRN/src/i18n';
import {ChartDataProps} from 'IOTRN/src/types';

export default class DeviceCharState {
  // 折线图上的单位
  selectedUnit: string = Strings.getLang(
    'rn_61004_panelhome_hour_textview_text',
  );
  // 折线图数据源
  chartData: ChartDataProps = {
    labels: [],
    data: [],
    unit: '',
    totalValue: 0,
  };
  // home折线图数据源
  homeChartData: ChartDataProps = {
    labels: [],
    data: [],
    unit: '',
    totalValue: 0,
  };
  // 实时记录小车当前位置
  carPosition: {latitude: number; longitude: number} = {
    latitude: 0,
    longitude: 0,
  };

  latestRegion = {};

  // 数据统计二氧化碳总排放量
  staticsTotalCo2Reduced = `0${Strings.getLang(
    'rn_61004_usagehistory_lbsunit_textview_text',
  )}`;
  localTimeStamp: number = 0;
  initialTimeStampFromDevice: number = 0;
  selectedTime: string = '';
  // 预约开关默认设置的时间戳
  defaultTimeStamp = 0;
  // 设备总工作时间
  totalMowingTime = '';
}
