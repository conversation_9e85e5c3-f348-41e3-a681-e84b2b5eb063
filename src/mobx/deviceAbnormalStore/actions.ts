import {computed} from 'mobx';
import DeviceAbnormalState from './state';
import {getInfoOfMessage, getNewResultFromErrorCode} from '../utils/help';
import {RootStore} from '../rootStore';
export default class DeviceAbnormalActions {
  state: DeviceAbnormalState;
  rootStore: RootStore;
  constructor(state: DeviceAbnormalState, rootStore: RootStore) {
    this.state = state;
    this.rootStore = rootStore;
  }

  // 获取设备故障被选中的index
  @computed get deviceErrorIndex() {
    const {deviceErrorSelectIndex = 0} = this.state;
    return deviceErrorSelectIndex;
  }

  // 首页status显示小红点 Device Status
  @computed get statusShowRedDot() {
    const {errorStatusList = []} = this.state;
    const result = errorStatusList.length > 0;
    return result;
  }

  /**
   * @description: 设置设备故障页面中目前被选中故障的index
   * @param {Number} value
   */
  setDeviceSelectedErrorIndex = (value = 0) => {
    this.state.deviceErrorSelectIndex = value;
  };

  /**
   * @description: 处理消息数据
   * 根据推送消息，对homeMsgList 做实时的更新，增加或者减少
   */
  dealWithErrorStatus = (value: {
    title: string;
    payloadData: {
      uuid: string;
      productId: string;
      messageType: string;
      createTime: string;
      deviceId: string;
      eventType: string;
      data: string;
    };
  }) => {
    const {filteredObj, newObj, errorCode, errorValue} = getInfoOfMessage(
      value,
      this.state,
    );
    const {homeMsgList = []} = this.state;
    const newResult = getNewResultFromErrorCode({
      filteredObj: filteredObj || {},
      newObj,
      homeMsgList,
      errorCode,
      errorValue,
    });
    this.setErrorStatusList(newResult);
  };

  // 设置主页消息列表
  setHomeMsgList = (value: {[key: string]: string}[]) => {
    if (value && Array.isArray(value)) {
      this.state.homeMsgList = value;
    } else {
      this.state.homeMsgList = [];
    }
  };
  // 设置主页消息列表
  setErrorStatusList = (value: {[key: string]: string}[]) => {
    if (value && Array.isArray(value)) {
      this.state.errorStatusList = value;
    } else {
      this.state.errorStatusList = [];
    }
  };

  /**
   * @description: 处理消息数据
   * 根据推送消息，对homeMsgList 做实时的更新，增加或者减少
   */
  dealWithMessageRes = (value: {
    title: string;
    payloadData: {
      uuid: string;
      productId: string;
      messageType: string;
      createTime: string;
      deviceId: string;
      eventType: string;
      data: string;
    };
  }) => {
    const {filteredObj, newObj, errorCode, errorValue} = getInfoOfMessage(
      value,
      {homeMsgList: this.state.homeMsgList},
    );
    const {homeMsgList = []} = this.state;
    const newResult = getNewResultFromErrorCode({
      filteredObj: filteredObj || {},
      newObj,
      homeMsgList,
      errorCode,
      errorValue,
    });
    // Remove console logs in production code
    this.setHomeMsgList(newResult);
  };
}
