/**
 * 聚合模块
 * 设备基础信息
 * **功能范围**：设备基础参数、连接状态、国际化配置等。
 */

import {makeAutoObservable} from 'mobx';
import DeviceStoreState from './state';
import DeviceBasicInfoActions from './actions';
import {BaseStore} from '../base/BaseStore';

export default class DeviceStore extends BaseStore {
  state: DeviceStoreState;
  actions: DeviceBasicInfoActions;
  // 构造函数，接收一个RootStore类型的参数
  constructor() {
    super();
    this.state = makeAutoObservable(new DeviceStoreState());
    this.actions = makeAutoObservable(new DeviceBasicInfoActions(this.state));
  }

  /**
   * 重置当前组件的状态
   */
  reset = () => {
    this.state = new DeviceStoreState();
  };
}
