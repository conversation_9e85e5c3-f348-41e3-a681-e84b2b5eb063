import {DeviceDetailProps, InitialParamsProps} from 'IOTRN/src/types/device';

// 导出一个默认的DeviceBasicInfoState类
export default class DeviceBasicInfoState {
  // 初始化参数
  initialParams: InitialParamsProps = {
    deviceId: '', // 设备ID
    mac: '', // MAC地址
    deviceName: '', // 设备名称
    appSettingOfHour: 0, // 北美12小时，欧洲24小时
    region: '', // 地区 NA/EU  EU:欧洲 US:北美 CN:中国
    timezone: '', // 时区
    productId: '',
    deviceDetail: '',
    appSettingOfUnit: '', // 温度单位
  };
  deviceDetail: DeviceDetailProps = {
    shareType: 0, // 共享类型
    infoStatus: 0, // 设备信息状态
    nickName: '', // 设备昵称
    deviceName: '', // 设备名称
    productId: '', // 产品ID
    commodityModel: '', // 商品型号
    sn: '', // 序列号
    deviceIcon: '',
    isOnline: 0, // 是否在线
    version: '', // 版本号
  };
  wifiName: string = '';

  // 预约加热刻度尺选中的刻度
  selectedTick: number = 0;
}
