/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2023-09-27 10:14:59
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-07-17 14:31:19
 * @FilePath: /61004/src/components/Disabled
 * @Description: Disabled View
 */
import React from 'react';
import {StyleSheet, View, ViewStyle} from 'react-native';
/**
 * 禁用状态组件
 * @param {Object} store - 注入的store对象
 * @param {Object} style - 自定义样式
 * @param {boolean} connectDisabled - 是否连接禁用状态
 * @param {boolean} disabled - 是否禁用
 * @returns {Node} - Disabled组件
 */
interface InjectedStores {
  style?: ViewStyle;
}

export const Disabled = ({style}: InjectedStores) => {
  return <View style={[styles.disabled, style]} />;
};

const styles = StyleSheet.create({
  disabled: {
    position: 'absolute',
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
    opacity: 0.4,
    backgroundColor: '#ffffff',
  },
});
