/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2023-09-27 10:14:59
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-07-17 16:37:04
 * @FilePath: /61004/src/pages/panel/components/NoContent
 * @Description: 无数据 卡片
 */
import React from 'react';
import {StyleSheet, View, Text} from 'react-native';
import {CVNIcon} from 'cvn-panel-kit';
import PropTypes from 'prop-types';
import Strings from 'IOTRN/src/i18n';
/**
 * 无数据占位
 * @param {Object} store - 注入的store对象
 * @param {Object} style - 自定义样式
 * @returns {Node} - NoContent组件
 */
export const NoContent = ({style = {}}) => {
  const tipText = Strings.getLang(
    'rn_61004_panelhome_usagedetailsnodata_textview_text',
  );
  return (
    <View style={[styles.boxView, style]}>
      <CVNIcon
        style={styles.icon}
        source={require('@assets/common/61004_common_bg_no_content.png')}
      />
      <Text style={styles.tipText}>{tipText}</Text>
    </View>
  );
};
NoContent.propTypes = {
  style: PropTypes.object,
};
const styles = StyleSheet.create({
  boxView: {
    marginTop: 5,
    alignItems: 'center',
  },
  icon: {
    width: 188 / 2,
    height: 240 / 2,
  },
  tipText: {
    fontSize: 15,
    color: '#999999',
  },
});
