/**
 * @description 错误回退UI组件
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  Platform,
} from 'react-native';
import {ErrorFallbackProps} from './types';
import Strings from '../../i18n';

const {width, height} = Dimensions.get('window');

// 全局错误回退组件
export const GlobalErrorFallback: React.FC<ErrorFallbackProps> = ({
  error,
  errorInfo,
  resetError,
  retryCount,
  canRetry,
}) => {
  const handleResetError = (): void => {
    resetError();
  };

  return (
    <View style={styles.globalContainer}>
      <View style={styles.errorContainer}>
        <Text style={styles.globalTitle}>
          {Strings.getLang('error_boundary_global_title') ?? '应用遇到了问题'}
        </Text>

        <Text style={styles.globalMessage}>
          {Strings.getLang('error_boundary_global_message') ??
            '很抱歉，应用遇到了意外错误。我们已经记录了这个问题。'}
        </Text>

        {__DEV__ && error && (
          <ScrollView style={styles.errorDetails}>
            <Text style={styles.errorTitle}>错误详情 (仅开发环境显示):</Text>
            <Text style={styles.errorText}>{error.message}</Text>
            {error.stack && (
              <Text style={styles.stackTrace}>{error.stack}</Text>
            )}
            {errorInfo && (
              <Text style={styles.stackTrace}>{errorInfo.componentStack}</Text>
            )}
          </ScrollView>
        )}

        <View style={styles.buttonContainer}>
          {canRetry && (
            <TouchableOpacity
              style={[styles.button, styles.retryButton]}
              onPress={handleResetError}>
              <Text style={styles.buttonText}>
                {retryCount > 0
                  ? Strings.getLang('error_boundary_retry_again') ??
                    `重试 (${retryCount})`
                  : Strings.getLang('error_boundary_retry') ?? '重试'}
              </Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity
            style={[styles.button, styles.reloadButton]}
            onPress={handleResetError}>
            <Text style={styles.buttonText}>
              {Strings.getLang('error_boundary_reload') ?? '重新加载'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

// 页面级错误回退组件
export const PageErrorFallback: React.FC<ErrorFallbackProps> = ({
  error,
  errorInfo,
  resetError,
  retryCount: _retryCount,
  canRetry,
  name,
}) => {
  const handleResetError = (): void => {
    resetError();
  };

  return (
    <View style={styles.pageContainer}>
      <View style={styles.errorContainer}>
        <Text style={styles.pageTitle}>
          {Strings.getLang('error_boundary_page_title') ?? '页面加载失败'}
        </Text>

        <Text style={styles.pageMessage}>
          {Strings.getLang('error_boundary_page_message') ??
            '当前页面遇到了问题，请尝试重新加载。'}
        </Text>

        {name && <Text style={styles.pageName}>页面: {name}</Text>}

        {__DEV__ && error && (
          <ScrollView style={styles.errorDetails}>
            <Text style={styles.errorTitle}>错误详情:</Text>
            <Text style={styles.errorText}>{error.message}</Text>
            {errorInfo && (
              <Text style={styles.stackTrace}>{errorInfo.componentStack}</Text>
            )}
          </ScrollView>
        )}

        <View style={styles.buttonContainer}>
          {canRetry && (
            <TouchableOpacity
              style={[styles.button, styles.retryButton]}
              onPress={handleResetError}>
              <Text style={styles.buttonText}>
                {Strings.getLang('error_boundary_retry_page') ?? '重新加载页面'}
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </View>
  );
};

// 组件级错误回退组件
export const ComponentErrorFallback: React.FC<ErrorFallbackProps> = ({
  error,
  resetError,
  canRetry,
  name,
}) => {
  const handleResetError = (): void => {
    resetError();
  };

  return (
    <View style={styles.componentContainer}>
      <Text style={styles.componentTitle}>
        {Strings.getLang('error_boundary_component_title') ?? '组件加载失败'}
      </Text>

      {name && <Text style={styles.componentName}>组件: {name}</Text>}

      {canRetry && (
        <TouchableOpacity
          style={[styles.button, styles.smallButton]}
          onPress={handleResetError}>
          <Text style={styles.smallButtonText}>
            {Strings.getLang('error_boundary_retry_component') ?? '重试'}
          </Text>
        </TouchableOpacity>
      )}

      {__DEV__ && error && (
        <Text style={styles.devErrorText}>{error.message}</Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  globalContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  pageContainer: {
    flex: 1,
    backgroundColor: '#ffffff',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  componentContainer: {
    backgroundColor: '#fff3cd',
    borderColor: '#ffeaa7',
    borderWidth: 1,
    borderRadius: 8,
    padding: 16,
    margin: 8,
    alignItems: 'center',
  },
  errorContainer: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 24,
    maxWidth: width * 0.9,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  globalTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#e74c3c',
    textAlign: 'center',
    marginBottom: 16,
  },
  pageTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#f39c12',
    textAlign: 'center',
    marginBottom: 12,
  },
  componentTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#e67e22',
    textAlign: 'center',
    marginBottom: 8,
  },
  globalMessage: {
    fontSize: 16,
    color: '#2c3e50',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 20,
  },
  pageMessage: {
    fontSize: 14,
    color: '#34495e',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 16,
  },
  pageName: {
    fontSize: 12,
    color: '#7f8c8d',
    textAlign: 'center',
    marginBottom: 12,
  },
  componentName: {
    fontSize: 12,
    color: '#95a5a6',
    textAlign: 'center',
    marginBottom: 8,
  },
  errorDetails: {
    maxHeight: height * 0.3,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    padding: 12,
    marginBottom: 20,
  },
  errorTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#e74c3c',
    marginBottom: 8,
  },
  errorText: {
    fontSize: 12,
    color: '#2c3e50',
    fontFamily: Platform.select({
      ios: 'Courier',
      android: 'monospace',
      default: 'monospace',
    }),
    marginBottom: 8,
  },
  stackTrace: {
    fontSize: 10,
    color: '#7f8c8d',
    fontFamily: Platform.select({
      ios: 'Courier',
      android: 'monospace',
      default: 'monospace',
    }),
  },
  devErrorText: {
    fontSize: 10,
    color: '#e74c3c',
    fontFamily: Platform.select({
      ios: 'Courier',
      android: 'monospace',
      default: 'monospace',
    }),
    marginTop: 8,
    textAlign: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 12,
  },
  button: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    minWidth: 100,
  },
  smallButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    minWidth: 60,
    backgroundColor: '#3498db',
  },
  retryButton: {
    backgroundColor: '#3498db',
  },
  reloadButton: {
    backgroundColor: '#2ecc71',
  },
  buttonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  smallButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
});

export default {
  GlobalErrorFallback,
  PageErrorFallback,
  ComponentErrorFallback,
};
