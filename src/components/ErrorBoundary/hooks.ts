/**
 * @description 错误边界相关Hooks
 */

import {useCallback, useEffect, useState} from 'react';
import {
  ErrorHandlerHookReturn,
  AsyncErrorHandlerHookReturn,
  ErrorRetryHookReturn,
  ErrorDetails,
} from './types';
import {ErrorOrNull} from '../../types/common';
import errorReportService from './ErrorReportService';
import logger from '../../utils/logger';

/**
 * 错误捕获Hook
 */
export function useErrorHandler(): ErrorHandlerHookReturn {
  const [error, setError] = useState<ErrorOrNull>(null);

  const captureError = useCallback(
    (capturedError: Error, context?: string): void => {
      const contextMessage = context ? ` in ${context}` : '';
      logger.error(`Error captured${contextMessage}:`, capturedError);

      // 报告错误
      const errorDetails: ErrorDetails = {
        error: capturedError,
        errorInfo: {
          componentStack: context ?? 'Unknown context',
        },
        timestamp: Date.now(),
      };

      errorReportService
        .reportError(errorDetails)
        .catch((reportError: Error) => {
          logger.error('Failed to report error:', reportError);
        });

      setError(capturedError);
    },
    [],
  );

  const clearError = useCallback((): void => {
    setError(null);
  }, []);

  return {
    error,
    captureError,
    clearError,
    hasError: error !== null,
  };
}

/**
 * 异步错误处理Hook
 */
export function useAsyncErrorHandler(): AsyncErrorHandlerHookReturn {
  const {captureError} = useErrorHandler();

  const executeAsync = useCallback(
    async <T>(
      asyncFn: () => Promise<T>,
      context?: string,
    ): Promise<T | null> => {
      try {
        return await asyncFn();
      } catch (error) {
        captureError(error as Error, context);
        return null;
      }
    },
    [captureError],
  );

  return {executeAsync};
}

/**
 * 全局错误处理Hook
 */
export function useGlobalErrorHandler(): void {
  useEffect(() => {
    // 检查ErrorUtils是否可用
    if (typeof ErrorUtils === 'undefined') {
      logger.warn('ErrorUtils is not available');
      return;
    }

    // 设置全局错误处理器
    const originalHandler = ErrorUtils.getGlobalHandler();

    const globalErrorHandler = (error: Error, isFatal?: boolean): void => {
      logger.error('Global error caught:', {error, isFatal});

      // 报告错误
      const errorDetails: ErrorDetails = {
        error: error instanceof Error ? error : new Error(String(error)),
        errorInfo: {
          componentStack: 'Global Error Handler',
        },
        timestamp: Date.now(),
      };

      errorReportService
        .reportError(errorDetails)
        .catch((reportError: Error) => {
          logger.error('Failed to report global error:', reportError);
        });

      // 调用原始处理器
      if (originalHandler) {
        originalHandler(error, isFatal);
      }
    };

    ErrorUtils.setGlobalHandler(globalErrorHandler);

    // 清理函数
    return (): void => {
      ErrorUtils.setGlobalHandler(originalHandler);
    };
  }, []);
}

/**
 * Promise拒绝处理Hook
 * 注意：React Native环境下不支持unhandledrejection事件
 * 此Hook在React Native中为空实现，保持API一致性
 */
export function useUnhandledPromiseRejectionHandler(): void {
  useEffect(() => {
    // React Native环境下不支持unhandledrejection事件
    // 这个Hook保持为空实现，确保API一致性
    logger.warn(
      'useUnhandledPromiseRejectionHandler: Not supported in React Native environment',
    );
  }, []);
}

/**
 * 错误重试Hook
 */
export function useErrorRetry(maxRetries = 3): ErrorRetryHookReturn {
  const [retryCount, setRetryCount] = useState<number>(0);
  const [isRetrying, setIsRetrying] = useState<boolean>(false);

  const retry = useCallback(
    async (
      retryFn: () => Promise<void> | void,
      delay = 1000,
    ): Promise<boolean> => {
      if (retryCount >= maxRetries) {
        logger.warn(`Max retries (${maxRetries}) exceeded`);
        return false;
      }

      setIsRetrying(true);
      setRetryCount(prev => prev + 1);

      try {
        // 延迟重试
        if (delay > 0) {
          await new Promise<void>(resolve => {
            setTimeout(resolve, delay);
          });
        }

        await retryFn();

        // 重试成功，重置计数
        setRetryCount(0);
        return true;
      } catch (error) {
        logger.error(`Retry ${retryCount + 1} failed:`, error);
        return false;
      } finally {
        setIsRetrying(false);
      }
    },
    [retryCount, maxRetries],
  );

  const reset = useCallback((): void => {
    setRetryCount(0);
    setIsRetrying(false);
  }, []);

  const canRetry = retryCount < maxRetries;

  return {
    retry,
    reset,
    retryCount,
    isRetrying,
    canRetry,
    maxRetries,
  };
}

/**
 * 错误边界状态Hook
 */
export function useErrorBoundaryState(): {
  readonly errorBoundaryInfo: {
    readonly hasError: boolean;
    readonly errorCount: number;
    readonly lastErrorTime: number | null;
  };
  readonly reportErrorBoundaryTrigger: (errorId: string) => void;
  readonly resetErrorBoundaryState: () => void;
} {
  const [errorBoundaryInfo, setErrorBoundaryInfo] = useState({
    hasError: false,
    errorCount: 0,
    lastErrorTime: null as number | null,
  });

  const reportErrorBoundaryTrigger = useCallback((errorId: string): void => {
    setErrorBoundaryInfo(prev => ({
      hasError: true,
      errorCount: prev.errorCount + 1,
      lastErrorTime: Date.now(),
    }));

    logger.warn(`Error boundary triggered: ${errorId}`);
  }, []);

  const resetErrorBoundaryState = useCallback((): void => {
    setErrorBoundaryInfo({
      hasError: false,
      errorCount: 0,
      lastErrorTime: null,
    });
  }, []);

  return {
    errorBoundaryInfo,
    reportErrorBoundaryTrigger,
    resetErrorBoundaryState,
  };
}
