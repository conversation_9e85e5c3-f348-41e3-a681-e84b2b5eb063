/**
 * @description 错误边界高阶组件
 */

import React from 'react';
import ErrorBoundary from './ErrorBoundary';
import {ErrorBoundaryProps} from './types';

/**
 * 错误边界高阶组件
 */
export function withErrorBoundary<P extends Record<string, unknown>>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<ErrorBoundaryProps, 'children'>,
): React.FC<P> {
  const WrappedComponent: React.FC<P> = props => {
    return (
      <ErrorBoundary {...errorBoundaryProps}>
        <Component {...props} />
      </ErrorBoundary>
    );
  };

  WrappedComponent.displayName = `withErrorBoundary(${
    Component.displayName ?? Component.name
  })`;

  return WrappedComponent;
}

/**
 * 页面级错误边界装饰器
 */
export function withPageErrorBoundary<P extends Record<string, unknown>>(
  Component: React.ComponentType<P>,
  pageName?: string,
): React.FC<P> {
  return withErrorBoundary(Component, {
    level: 'page',
    name: pageName ?? Component.displayName ?? Component.name,
    enableRetry: true,
    maxRetries: 2,
  });
}

/**
 * 组件级错误边界装饰器
 */
export function withComponentErrorBoundary<P extends Record<string, unknown>>(
  Component: React.ComponentType<P>,
  componentName?: string,
): React.FC<P> {
  return withErrorBoundary(Component, {
    level: 'component',
    name: componentName ?? Component.displayName ?? Component.name,
    enableRetry: true,
    maxRetries: 1,
    isolate: true,
  });
}

/**
 * 全局错误边界装饰器
 */
export function withGlobalErrorBoundary<P extends Record<string, unknown>>(
  Component: React.ComponentType<P>,
): React.FC<P> {
  return withErrorBoundary(Component, {
    level: 'global',
    name: 'GlobalApp',
    enableRetry: true,
    maxRetries: 1,
  });
}

export default withErrorBoundary;
