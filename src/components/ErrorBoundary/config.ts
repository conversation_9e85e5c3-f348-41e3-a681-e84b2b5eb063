/**
 * @description 错误边界配置
 */

import {ErrorBoundaryConfig} from './types';

// 默认配置
export const defaultErrorBoundaryConfig: ErrorBoundaryConfig = {
  enableErrorReporting: true,
  enableRetry: true,
  maxRetries: 3,
  retryDelay: 1000,

  showErrorDetails: __DEV__,
  logErrors: true,

  enableCrashReporting: !__DEV__,

  showRetryButton: true,
  showReloadButton: true,
  customErrorMessages: {
    global: '应用遇到了问题，请重试或重新启动应用',
    page: '页面加载失败，请重试',
    component: '组件加载失败',
  },
};

// 环境特定配置
export const getErrorBoundaryConfig = (): ErrorBoundaryConfig => {
  if (__DEV__) {
    return {
      ...defaultErrorBoundaryConfig,
      enableErrorReporting: false,
      showErrorDetails: true,
      enableCrashReporting: false,
      maxRetries: 5, // 开发环境允许更多重试
    };
  }

  return {
    ...defaultErrorBoundaryConfig,
    enableErrorReporting: true,
    showErrorDetails: false,
    enableCrashReporting: true,
  };
};

export default getErrorBoundaryConfig;
