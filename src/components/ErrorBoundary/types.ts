/**
 * @description 错误边界相关类型定义
 */

import {ReactNode, ComponentType} from 'react';
import {ErrorOrNull, StringOrNull} from '../../types/common';

export interface ErrorInfo {
  readonly componentStack: string;
  readonly errorBoundary?: string;
  readonly errorBoundaryStack?: string;
}

export interface DeviceInfo {
  readonly platform: string;
  readonly version: string;
  readonly model: string;
}

export interface ErrorDetails {
  readonly error: Error;
  readonly errorInfo: ErrorInfo;
  readonly timestamp: number;
  readonly userId?: string;
  readonly deviceInfo?: DeviceInfo;
  readonly appVersion?: string;
  readonly route?: string;
  readonly userAgent?: string;
}

export interface ErrorBoundaryState {
  readonly hasError: boolean;
  readonly error: ErrorOrNull;
  readonly errorInfo: ErrorInfo | null;
  readonly errorId: StringOrNull;
  readonly retryCount: number;
}

export type ErrorBoundaryLevel = 'global' | 'page' | 'component';

export interface ErrorFallbackProps {
  readonly error: ErrorOrNull;
  readonly errorInfo: ErrorInfo | null;
  readonly resetError: () => void;
  readonly retryCount: number;
  readonly canRetry: boolean;
  readonly level: ErrorBoundaryLevel;
  readonly name?: string;
}

export interface ErrorBoundaryProps {
  readonly children: ReactNode;
  readonly fallback?: ComponentType<ErrorFallbackProps>;
  readonly onError?: (error: Error, errorInfo: ErrorInfo) => void;
  readonly isolate?: boolean;
  readonly level?: ErrorBoundaryLevel;
  readonly name?: string;
  readonly enableRetry?: boolean;
  readonly maxRetries?: number;
}

export interface ErrorReportService {
  reportError: (errorDetails: ErrorDetails) => Promise<void>;
  reportCrash: (errorDetails: ErrorDetails) => Promise<void>;
}

export interface ErrorRecoveryOptions {
  readonly autoRetry?: boolean;
  readonly retryDelay?: number;
  readonly maxRetries?: number;
  readonly fallbackComponent?: ComponentType<ErrorFallbackProps>;
}

export interface ErrorHandlerHookReturn {
  readonly error: ErrorOrNull;
  readonly captureError: (error: Error, context?: string) => void;
  readonly clearError: () => void;
  readonly hasError: boolean;
}

export interface AsyncErrorHandlerHookReturn {
  readonly executeAsync: <T>(
    asyncFn: () => Promise<T>,
    context?: string,
  ) => Promise<T | null>;
}

export interface ErrorRetryHookReturn {
  readonly retry: (
    retryFn: () => Promise<void> | void,
    delay?: number,
  ) => Promise<boolean>;
  readonly reset: () => void;
  readonly retryCount: number;
  readonly isRetrying: boolean;
  readonly canRetry: boolean;
  readonly maxRetries: number;
}

export interface ErrorBoundaryConfig {
  readonly enableErrorReporting: boolean;
  readonly enableRetry: boolean;
  readonly maxRetries: number;
  readonly retryDelay: number;
  readonly showErrorDetails: boolean;
  readonly logErrors: boolean;
  readonly reportingEndpoint?: string;
  readonly reportingApiKey?: string;
  readonly enableCrashReporting: boolean;
  readonly showRetryButton: boolean;
  readonly showReloadButton: boolean;
  readonly customErrorMessages: {
    readonly global: string;
    readonly page: string;
    readonly component: string;
  };
}

export interface ErrorQueueItem {
  readonly errorDetails: ErrorDetails;
  readonly timestamp: number;
  readonly attempts: number;
}

export interface ErrorReportPayload {
  readonly error: {
    readonly name: string;
    readonly message: string;
    readonly stack?: string;
  };
  readonly errorInfo: ErrorInfo;
  readonly deviceInfo?: DeviceInfo;
  readonly appVersion?: string;
  readonly userAgent?: string;
  readonly route?: string;
  readonly timestamp: number;
  readonly isCrash: boolean;
  readonly reportedAt: string;
}
