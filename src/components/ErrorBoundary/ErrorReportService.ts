/**
 * @description 错误报告服务
 */

import {Platform} from 'react-native';
import {
  ErrorDetails,
  ErrorReportService,
  DeviceInfo,
  ErrorQueueItem,
  ErrorReportPayload,
} from './types';
import logger from '../../utils/logger';

// 动态导入DeviceInfo以避免在某些环境下的导入错误
interface DeviceInfoModule {
  getModel: () => Promise<string>;
  getVersion: () => Promise<string>;
  getBuildNumber: () => Promise<string>;
  getUserAgent: () => Promise<string>;
}

let DeviceInfoModule: DeviceInfoModule | null = null;

try {
  DeviceInfoModule = require('react-native-device-info') as DeviceInfoModule;
} catch (error) {
  logger.warn('react-native-device-info not available:', error);
}

class ErrorReportServiceImpl implements ErrorReportService {
  private readonly errorQueue: ErrorQueueItem[] = [];
  private isReporting = false;
  private readonly maxQueueSize = 50;
  private readonly maxRetryAttempts = 3;

  /**
   * 报告错误到远程服务
   */
  public async reportError(errorDetails: ErrorDetails): Promise<void> {
    try {
      const enrichedError = await this.enrichErrorDetails(errorDetails);
      this.addToQueue(enrichedError);
      await this.processQueue();
      this.logErrorLocally(enrichedError);
    } catch (error) {
      logger.error('Failed to report error:', error);
    }
  }

  /**
   * 报告崩溃信息
   */
  public async reportCrash(errorDetails: ErrorDetails): Promise<void> {
    try {
      const enrichedError = await this.enrichErrorDetails(errorDetails);
      await this.sendErrorToRemote(enrichedError, true);
      this.logErrorLocally(enrichedError);
    } catch (error) {
      logger.error('Failed to report crash:', error);
    }
  }

  /**
   * 丰富错误详情
   */
  private async enrichErrorDetails(
    errorDetails: ErrorDetails,
  ): Promise<ErrorDetails> {
    try {
      const deviceInfo: DeviceInfo = {
        platform: Platform.OS,
        version: Platform.Version.toString(),
        model: DeviceInfoModule ? await DeviceInfoModule.getModel() : 'Unknown',
      };

      const appVersion = DeviceInfoModule
        ? await DeviceInfoModule.getVersion()
        : '1.0.0';
      const buildNumber = DeviceInfoModule
        ? await DeviceInfoModule.getBuildNumber()
        : '1';
      const userAgent = DeviceInfoModule
        ? await DeviceInfoModule.getUserAgent()
        : 'Unknown';

      return {
        ...errorDetails,
        deviceInfo,
        appVersion: `${appVersion} (${buildNumber})`,
        userAgent,
      };
    } catch (error) {
      logger.warn('Failed to enrich error details:', error);
      return {
        ...errorDetails,
        deviceInfo: {
          platform: Platform.OS,
          version: Platform.Version.toString(),
          model: 'Unknown',
        },
        appVersion: '1.0.0 (1)',
        userAgent: 'Unknown',
      };
    }
  }

  /**
   * 添加错误到队列
   */
  private addToQueue(errorDetails: ErrorDetails): void {
    const queueItem: ErrorQueueItem = {
      errorDetails,
      timestamp: Date.now(),
      attempts: 0,
    };

    this.errorQueue.push(queueItem);

    // 限制队列大小
    if (this.errorQueue.length > this.maxQueueSize) {
      this.errorQueue.shift();
    }
  }

  /**
   * 处理错误队列
   */
  private async processQueue(): Promise<void> {
    if (this.isReporting || this.errorQueue.length === 0) {
      return;
    }

    this.isReporting = true;

    try {
      const itemsToProcess = [...this.errorQueue];
      this.errorQueue.length = 0;

      for (const item of itemsToProcess) {
        try {
          await this.sendErrorToRemote(item.errorDetails);
        } catch (error) {
          // 重新加入队列进行重试
          if (item.attempts < this.maxRetryAttempts) {
            this.errorQueue.push({
              ...item,
              attempts: item.attempts + 1,
            });
          }
          logger.error('Failed to send error report:', error);
        }
      }
    } catch (error) {
      logger.error('Failed to process error queue:', error);
    } finally {
      this.isReporting = false;
    }
  }

  /**
   * 发送错误到远程服务
   */
  private async sendErrorToRemote(
    errorDetails: ErrorDetails,
    isCrash = false,
  ): Promise<void> {
    try {
      const payload: ErrorReportPayload = {
        error: {
          name: errorDetails.error.name,
          message: errorDetails.error.message,
          ...(errorDetails.error.stack && {stack: errorDetails.error.stack}),
        },
        errorInfo: errorDetails.errorInfo,
        ...(errorDetails.deviceInfo && {deviceInfo: errorDetails.deviceInfo}),
        ...(errorDetails.appVersion && {appVersion: errorDetails.appVersion}),
        ...(errorDetails.userAgent && {userAgent: errorDetails.userAgent}),
        ...(errorDetails.route && {route: errorDetails.route}),
        timestamp: errorDetails.timestamp,
        isCrash,
        reportedAt: new Date().toISOString(),
      };

      // 开发环境下只记录日志
      if (__DEV__) {
        logger.warn('Error Report (DEV):', payload);
        return;
      }

      // 生产环境下发送到实际的错误报告服务
      // 这里需要根据项目需求集成具体的错误报告服务
      // 例如：Sentry, Bugsnag, 或自定义API
      await this.sendToErrorReportingService(payload);

      logger.warn('Error reported successfully');
    } catch (error) {
      logger.error('Failed to send error to remote:', error);
      throw error;
    }
  }

  /**
   * 发送到错误报告服务
   */
  private async sendToErrorReportingService(
    payload: ErrorReportPayload,
  ): Promise<void> {
    // 这里实现实际的错误报告服务集成
    // 需要根据项目需求集成具体的错误报告服务
    // 例如：Sentry, Bugsnag, 或自定义API

    // 临时实现：只记录日志
    logger.warn('Would send error report:', payload);

    // 示例：await this.sendToSentry(payload);
    // 示例：await this.sendToBugsnag(payload);
    // 示例：await this.sendToCustomAPI(payload);
  }

  /**
   * 本地日志记录
   */
  private logErrorLocally(errorDetails: ErrorDetails): void {
    const logEntry = {
      timestamp: errorDetails.timestamp,
      error: {
        name: errorDetails.error.name,
        message: errorDetails.error.message,
        stack: errorDetails.error.stack,
      },
      componentStack: errorDetails.errorInfo.componentStack,
      route: errorDetails.route,
      deviceInfo: errorDetails.deviceInfo,
    };

    logger.error('Error Boundary Caught Error:', logEntry);
  }

  /**
   * 获取队列状态
   */
  public getQueueStatus(): {queueSize: number; isReporting: boolean} {
    return {
      queueSize: this.errorQueue.length,
      isReporting: this.isReporting,
    };
  }

  /**
   * 清空错误队列
   */
  public clearQueue(): void {
    this.errorQueue.length = 0;
  }
}

// 单例实例
export const errorReportService = new ErrorReportServiceImpl();
export default errorReportService;
