/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2024-05-24 15:30:44
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-07-17 14:30:01
 * @FilePath: /61004/src/components/Item
 * @Description: 每一行Item组件
 */
import React from 'react';
import {Item} from 'cvn-panel-kit';

/**
 * 下划线组件
 * @param {...res} - 其他属性
 * @returns {Node} - ItemView组件
 */
const ItemView = ({...res}) => {
  return (
    <Item
      rightIconSource={require('@assets/common/61004_common_icon_arrow_right.png')}
      {...res}
    />
  );
};
export {ItemView as Item};
export default ItemView;
