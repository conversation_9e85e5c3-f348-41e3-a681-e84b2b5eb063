// ModalContext
import React, {
  createContext,
  useState,
  useContext,
  useMemo,
  useCallback,
} from 'react';
import {Text, StyleSheet} from 'react-native';
import {Modal} from '@ant-design/react-native';

interface ModalProps {
  title?: string;
  content?: string;
  onConfirm?: () => void;
  onCancel?: () => void;
  contentRender?: React.ReactNode;
}

interface ModalContextType {
  showModal: (props: ModalProps) => void;
  hideModal: () => void;
}

// 创建 Modal 上下文
const ModalContext = createContext<ModalContextType>({
  showModal: () => {},
  hideModal: () => {},
  // 其他必要属性
});

// ModalProvider 组件，负责提供 modal 状态和控制方法
export const ModalProvider = ({children}: {children: React.ReactNode}) => {
  const [visible, setVisible] = useState(false);
  const [modalProps, setModalProps] = useState<ModalProps>({
    title: '',
    content: '',
    onConfirm: () => {},
    onCancel: () => {},
    contentRender: null,
  });

  // 显示 Modal
  const showModal = useCallback((props: ModalProps) => {
    setModalProps(prevProps => ({...prevProps, ...props}));
    setVisible(true);
  }, []);

  // 关闭 Modal
  const hideModal = useCallback(() => {
    setVisible(false);
  }, []);

  const contextValue = useMemo(
    () => ({
      showModal,
      hideModal,
    }),
    [showModal, hideModal],
  );

  return (
    <ModalContext.Provider value={contextValue}>
      {children}
      {/* 这个 Modal 是全局共享的 */}
      {/** @ts-ignore */}
      <Modal
        visible={visible}
        style={styles.modalStyle}
        bodyStyle={styles.bodyStyle}
        transparent
        maskClosable={false}
        title={modalProps.title}
        onClose={hideModal}
        footer={[
          {
            // @ts-ignore
            text: <Text style={styles.confirmStyle}>OK</Text>,
            onPress: modalProps.onConfirm || hideModal,
          },
        ]}>
        {modalProps.contentRender}
        <Text style={styles.content}>{modalProps.content}</Text>
      </Modal>
    </ModalContext.Provider>
  );
};

// 使用 Context 来获取 Modal 的方法
export const useModal = () => {
  return useContext(ModalContext);
};

const styles = StyleSheet.create({
  modalStyle: {
    width: 270,
    borderRadius: 10,
    minHeight: 175,
  },
  bodyStyle: {
    minHeight: 125,
    justifyContent: 'center',
    alignContent: 'center',
    paddingLeft: 20,
    paddingRight: 20,
  },
  confirmStyle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#77BC1F',
    lineHeight: 19,
  },
  content: {
    fontSize: 14,
    color: '#000000',
    lineHeight: 19,
    textAlign: 'center',
  },
});
