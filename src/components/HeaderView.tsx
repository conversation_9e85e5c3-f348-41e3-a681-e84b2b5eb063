/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2023-02-09 11:35:30
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-07-17 14:32:32
 * @FilePath: /61004/src/components/HeaderView
 * @Description: HeaderView二次封装
 */
import React from 'react';
import {HeaderView} from 'cvn-panel-kit';
import {tracer, eleIdMap} from '@tracer';

/**
 * 头部组件
 * @param {...res} - 其他属性
 * @returns {Node} - Header组件
 */
const Header = ({...res}) => {
  const {onLeftPress = () => {}, useCommonEleId = true} = res;
  return (
    <HeaderView
      backIconSource={require('@assets/common/61004_common_icon_arrow_left_new.png')}
      {...res}
      onLeftPress={() => {
        // 覆盖传过来的方法，增加埋点
        if (useCommonEleId) {
          tracer.click({eleid: eleIdMap.Return_Button_Click});
        }
        onLeftPress();
      }}
    />
  );
};
export default Header;
