/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2023-03-23 10:53:09
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2024-09-11 11:10:00
 * @FilePath: /61004/App.js
 * @Description: app入口
 */
import React, {useCallback, useEffect, useState} from 'react';
import {Provider as MobxProvider} from 'mobx-react';
import {NavigationContainer} from '@react-navigation/native';
import HomeStackScreen from './src/pages/stacks/HomeStackScreen';
import {Provider} from '@ant-design/react-native';
import Strings from './src/i18n';
import {LogBox} from 'react-native';
import {routeListener} from './src/utils/log';
import {ModalProvider} from './src/components/Modal';
import rootStore from './src/mobx/rootStore';
// Import our logger to ensure it's initialized early
import './src/utils/logger';
// Import error boundary components
import {
  ErrorBoundary,
  useGlobalErrorHandler,
  useUnhandledPromiseRejectionHandler,
} from './src/components/ErrorBoundary';

const App = (props: {lang: string}) => {
  const [langDidLoad, setLangDidLoad] = useState(false);

  // 启用全局错误处理
  useGlobalErrorHandler();
  useUnhandledPromiseRejectionHandler();

  const getLanguage = useCallback(() => {
    const lang = props?.lang ?? 'en';
    Strings.forceUpdateNetworkLang(lang).then(() => {
      setLangDidLoad(true);
    });
  }, [props?.lang]);

  useEffect(() => {
    LogBox.ignoreLogs([
      'Non-serializable values were found in the navigation state',
    ]);
    getLanguage();
  }, [getLanguage, props?.lang]);

  return langDidLoad ? (
    <ErrorBoundary
      level="global"
      name="App"
      enableRetry={true}
      maxRetries={1}
      onError={(error, errorInfo) => {
        console.error('Global App Error:', error, errorInfo);
      }}>
      {/* @ts-ignore */}
      <Provider theme={{primaryColor: '#1DA57A'}}>
        <MobxProvider rootStore={rootStore}>
          <TabRootNav {...props} />
        </MobxProvider>
      </Provider>
    </ErrorBoundary>
  ) : null;
};

export default App;

const TabRootNav = ({...props}) => {
  return (
    <NavigationContainer
      onStateChange={res => {
        routeListener(res);
      }}>
      <ModalProvider>
        <HomeStackScreen {...props} />
      </ModalProvider>
    </NavigationContainer>
  );
};
